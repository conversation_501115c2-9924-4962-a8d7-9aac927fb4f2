"""
Tests for the FastAPI application
"""

import pytest
from fastapi.testclient import TestClient
import json
from main import app

client = TestClient(app)


class TestAPI:
    
    def test_root_endpoint(self):
        """Test the root endpoint"""
        response = client.get("/")
        assert response.status_code == 200
        assert "message" in response.json()
    
    def test_health_endpoint(self):
        """Test the health check endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "version" in data
    
    def test_analyze_endpoint_valid_request(self):
        """Test the analyze endpoint with valid request"""
        request_data = {
            "data_info": {
                "columns": ["id", "name", "age", "salary"],
                "dtypes": {
                    "id": "int64",
                    "name": "object",
                    "age": "int64", 
                    "salary": "float64"
                },
                "shape": [100, 4],
                "sample_data": [
                    {"id": 1, "name": "<PERSON>", "age": 25, "salary": 50000},
                    {"id": 2, "name": "<PERSON>", "age": 30, "salary": 60000}
                ]
            },
            "analysis_type": "eda",
            "data_source": "metadata_only",
            "file_type": "csv"
        }
        
        response = client.post("/analyze", json=request_data)
        assert response.status_code == 200
        
        data = response.json()
        assert "plan" in data
        assert "code" in data
        assert "metadata" in data
        assert isinstance(data["plan"], str)
        assert isinstance(data["code"], str)
    
    def test_analyze_endpoint_missing_data_info(self):
        """Test analyze endpoint with missing data_info"""
        request_data = {
            "analysis_type": "eda",
            "data_source": "metadata_only"
        }
        
        response = client.post("/analyze", json=request_data)
        assert response.status_code == 422  # Validation error
    
    def test_analyze_endpoint_invalid_analysis_type(self):
        """Test analyze endpoint with invalid analysis type"""
        request_data = {
            "data_info": {
                "columns": ["col1", "col2"],
                "dtypes": {"col1": "int64", "col2": "object"},
                "shape": [100, 2]
            },
            "analysis_type": "invalid_type",
            "data_source": "metadata_only"
        }
        
        response = client.post("/analyze", json=request_data)
        assert response.status_code == 400
        assert "Analysis type must be one of" in response.json()["detail"]
    
    def test_analyze_endpoint_all_analysis_types(self):
        """Test all supported analysis types"""
        base_request = {
            "data_info": {
                "columns": ["id", "feature1", "feature2", "target"],
                "dtypes": {
                    "id": "int64",
                    "feature1": "float64",
                    "feature2": "float64",
                    "target": "object"
                },
                "shape": [1000, 4],
                "sample_data": [
                    {"id": 1, "feature1": 1.5, "feature2": 2.3, "target": "A"},
                    {"id": 2, "feature1": 2.1, "feature2": 1.8, "target": "B"}
                ]
            },
            "data_source": "metadata_only",
            "file_type": "csv"
        }
        
        analysis_types = ["eda", "visualization", "ml", "stats"]
        
        for analysis_type in analysis_types:
            request_data = base_request.copy()
            request_data["analysis_type"] = analysis_type
            
            response = client.post("/analyze", json=request_data)
            assert response.status_code == 200, f"Failed for analysis type: {analysis_type}"
            
            data = response.json()
            assert "plan" in data
            assert "code" in data
            assert len(data["plan"]) > 0
            assert len(data["code"]) > 0
    
    def test_analyze_endpoint_with_user_request(self):
        """Test analyze endpoint with user request"""
        request_data = {
            "data_info": {
                "columns": ["price", "bedrooms", "location"],
                "dtypes": {
                    "price": "float64",
                    "bedrooms": "int64",
                    "location": "object"
                },
                "shape": [500, 3]
            },
            "analysis_type": "ml",
            "user_request": "Predict house prices based on bedrooms and location",
            "data_source": "metadata_only",
            "file_type": "csv"
        }
        
        response = client.post("/analyze", json=request_data)
        assert response.status_code == 200
        
        data = response.json()
        assert "predict house prices" in data["plan"].lower() or "predict house prices" in data["code"].lower()
    
    def test_templates_endpoint(self):
        """Test the templates endpoint"""
        response = client.get("/templates")
        assert response.status_code == 200
        
        data = response.json()
        assert "eda" in data
        assert "visualization" in data
        assert "ml" in data
        assert "stats" in data
        
        # Check that descriptions are provided
        for template_type, description in data.items():
            assert isinstance(description, str)
            assert len(description) > 0
    
    def test_supported_formats_endpoint(self):
        """Test the supported formats endpoint"""
        response = client.get("/supported-formats")
        assert response.status_code == 200
        
        data = response.json()
        assert "formats" in data
        assert "max_size_mb" in data
        assert "sampling_threshold_rows" in data
        
        assert isinstance(data["formats"], list)
        assert "csv" in data["formats"]
        assert isinstance(data["max_size_mb"], int)
        assert isinstance(data["sampling_threshold_rows"], int)
    
    def test_analyze_file_endpoint_no_file(self):
        """Test analyze-file endpoint without file"""
        response = client.post("/analyze-file")
        assert response.status_code == 422  # Missing required file
    
    def test_large_dataset_handling(self):
        """Test handling of large datasets"""
        request_data = {
            "data_info": {
                "columns": ["col1", "col2", "col3"],
                "dtypes": {
                    "col1": "int64",
                    "col2": "float64", 
                    "col3": "object"
                },
                "shape": [200000, 3],  # Large dataset
                "sample_data": [
                    {"col1": 1, "col2": 1.5, "col3": "A"},
                    {"col1": 2, "col2": 2.5, "col3": "B"}
                ]
            },
            "analysis_type": "eda",
            "data_source": "metadata_only",
            "file_type": "csv"
        }
        
        response = client.post("/analyze", json=request_data)
        assert response.status_code == 200
        
        data = response.json()
        # Should suggest sampling for large datasets
        assert data["metadata"]["suggested_sampling"] is not None
    
    def test_error_handling(self):
        """Test error handling for malformed requests"""
        # Test with completely invalid JSON structure
        response = client.post("/analyze", json={"invalid": "structure"})
        assert response.status_code == 422
        
        # Test with empty request
        response = client.post("/analyze", json={})
        assert response.status_code == 422


if __name__ == "__main__":
    pytest.main([__file__])
