#!/usr/bin/env python3
"""
Validate that the Chrome extension is properly set up
"""

import os
import json
from pathlib import Path

def check_file_exists(file_path, description):
    """Check if a file exists and report status"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (MISSING)")
        return False

def validate_manifest():
    """Validate the manifest.json file"""
    print("🔍 Validating manifest.json...")
    
    manifest_path = "extension/manifest.json"
    
    if not check_file_exists(manifest_path, "Manifest file"):
        return False
    
    try:
        with open(manifest_path, 'r') as f:
            manifest = json.load(f)
        
        # Check required fields
        required_fields = ["manifest_version", "name", "version", "permissions"]
        for field in required_fields:
            if field in manifest:
                print(f"✅ Manifest has required field: {field}")
            else:
                print(f"❌ Manifest missing required field: {field}")
                return False
        
        # Check manifest version
        if manifest.get("manifest_version") == 3:
            print("✅ Using Manifest V3 (correct)")
        else:
            print(f"⚠️  Using Manifest V{manifest.get('manifest_version')} (should be 3)")
        
        # Check permissions
        permissions = manifest.get("permissions", [])
        required_permissions = ["activeTab", "storage", "scripting"]
        for perm in required_permissions:
            if perm in permissions:
                print(f"✅ Has permission: {perm}")
            else:
                print(f"❌ Missing permission: {perm}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in manifest: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading manifest: {e}")
        return False

def validate_files():
    """Validate all required extension files"""
    print("\n📁 Validating extension files...")
    
    required_files = [
        ("extension/manifest.json", "Manifest file"),
        ("extension/background.js", "Background script"),
        ("extension/content.js", "Content script"),
        ("extension/styles.css", "Styles"),
        ("extension/popup/popup.html", "Popup HTML"),
        ("extension/popup/popup.css", "Popup CSS"),
        ("extension/popup/popup.js", "Popup JavaScript"),
        ("extension/utils/kaggle-detector.js", "Kaggle detector utility"),
        ("extension/icons/icon16.png", "16x16 icon"),
        ("extension/icons/icon48.png", "48x48 icon"),
        ("extension/icons/icon128.png", "128x128 icon"),
    ]
    
    all_exist = True
    for file_path, description in required_files:
        if not check_file_exists(file_path, description):
            all_exist = False
    
    return all_exist

def validate_icon_sizes():
    """Validate icon file sizes"""
    print("\n🖼️  Validating icon sizes...")
    
    try:
        from PIL import Image
        
        icon_sizes = [
            ("extension/icons/icon16.png", 16),
            ("extension/icons/icon48.png", 48),
            ("extension/icons/icon128.png", 128),
        ]
        
        all_correct = True
        for icon_path, expected_size in icon_sizes:
            if os.path.exists(icon_path):
                try:
                    with Image.open(icon_path) as img:
                        width, height = img.size
                        if width == expected_size and height == expected_size:
                            print(f"✅ {icon_path}: {width}x{height} (correct)")
                        else:
                            print(f"⚠️  {icon_path}: {width}x{height} (expected {expected_size}x{expected_size})")
                            all_correct = False
                except Exception as e:
                    print(f"❌ Error reading {icon_path}: {e}")
                    all_correct = False
            else:
                print(f"❌ {icon_path}: Missing")
                all_correct = False
        
        return all_correct
        
    except ImportError:
        print("⚠️  PIL not available, skipping icon size validation")
        print("   Install with: pip install Pillow")
        return True

def validate_configuration():
    """Validate extension configuration"""
    print("\n⚙️  Validating configuration...")
    
    # Check background.js for correct URLs
    bg_path = "extension/background.js"
    if os.path.exists(bg_path):
        with open(bg_path, 'r') as f:
            content = f.read()
            
        if "sabryroby.app.n8n.cloud" in content:
            print("✅ Background script has correct n8n URL")
        else:
            print("⚠️  Background script may not have correct n8n URL")
        
        if "localhost:8000" in content:
            print("✅ Background script has analysis engine URL")
        else:
            print("⚠️  Background script may not have analysis engine URL")
    
    return True

def main():
    """Run all validations"""
    print("🔍 Chrome Extension Validation")
    print("=" * 50)
    
    # Change to project directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(script_dir)
    os.chdir(project_dir)
    
    print(f"Project directory: {os.getcwd()}")
    
    # Run validations
    validations = [
        ("Manifest", validate_manifest),
        ("Files", validate_files),
        ("Icons", validate_icon_sizes),
        ("Configuration", validate_configuration),
    ]
    
    results = {}
    for name, validation_func in validations:
        try:
            results[name] = validation_func()
        except Exception as e:
            print(f"❌ Error in {name} validation: {e}")
            results[name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 VALIDATION SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{name:<15} {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All validations passed!")
        print("\nNext steps:")
        print("1. Open Chrome and go to chrome://extensions/")
        print("2. Enable 'Developer mode'")
        print("3. Click 'Load unpacked'")
        print("4. Select the 'extension' folder")
        print("5. The extension should load successfully!")
    else:
        print("\n⚠️  Some validations failed.")
        print("Please fix the issues above before loading the extension.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
