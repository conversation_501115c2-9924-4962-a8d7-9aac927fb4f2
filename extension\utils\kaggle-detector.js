/**
 * Kaggle data detection utility
 * Injected into Kaggle pages to detect available data
 */

(function() {
    'use strict';
    
    /**
     * Main detection function
     */
    function detectKaggleData() {
        const dataInfo = {
            files: [],
            dataframes: [],
            variables: [],
            notebooks: [],
            datasets: []
        };
        
        try {
            // Detect uploaded files
            detectUploadedFiles(dataInfo);
            
            // Detect dataset files
            detectDatasetFiles(dataInfo);
            
            // Detect DataFrames in code
            detectDataFrames(dataInfo);
            
            // Detect variables
            detectVariables(dataInfo);
            
            // Get notebook metadata
            getNotebookMetadata(dataInfo);
            
        } catch (error) {
            console.error('Data detection error:', error);
        }
        
        return {
            success: true,
            dataInfo: dataInfo,
            timestamp: new Date().toISOString()
        };
    }
    
    /**
     * Detect uploaded files in the notebook
     */
    function detectUploadedFiles(dataInfo) {
        // Look for file upload elements
        const fileSelectors = [
            '[data-testid="file-item"]',
            '.file-item',
            '.input-file',
            '.uploaded-file',
            '.dataset-file'
        ];
        
        fileSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                const fileName = extractFileName(element);
                if (fileName && isDataFile(fileName)) {
                    dataInfo.files.push({
                        name: fileName,
                        type: getFileType(fileName),
                        source: 'upload',
                        element: getElementInfo(element)
                    });
                }
            });
        });
        
        // Look for file references in sidebar or file explorer
        const sidebarFiles = document.querySelectorAll('.sidebar-file, .file-explorer-item');
        sidebarFiles.forEach(element => {
            const fileName = element.textContent?.trim();
            if (fileName && isDataFile(fileName)) {
                dataInfo.files.push({
                    name: fileName,
                    type: getFileType(fileName),
                    source: 'sidebar',
                    element: getElementInfo(element)
                });
            }
        });
    }
    
    /**
     * Detect dataset files from Kaggle datasets
     */
    function detectDatasetFiles(dataInfo) {
        // Look for dataset references
        const datasetSelectors = [
            '[data-testid="dataset-file"]',
            '.dataset-item',
            '.competition-file'
        ];
        
        datasetSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                const fileName = extractFileName(element);
                if (fileName && isDataFile(fileName)) {
                    dataInfo.datasets.push({
                        name: fileName,
                        type: getFileType(fileName),
                        source: 'dataset',
                        element: getElementInfo(element)
                    });
                }
            });
        });
    }
    
    /**
     * Detect DataFrames and data variables in code cells
     */
    function detectDataFrames(dataInfo) {
        const codeCells = document.querySelectorAll('.code-cell, [data-testid="code-cell"], .input-cell');
        
        codeCells.forEach((cell, index) => {
            const code = cell.textContent || '';
            
            // Patterns for DataFrame creation
            const patterns = [
                // pd.read_csv, pd.read_excel, etc.
                /(\w+)\s*=\s*pd\.read_(\w+)\s*\(\s*['"]([^'"]+)['"].*?\)/g,
                // Direct DataFrame creation
                /(\w+)\s*=\s*pd\.DataFrame\s*\(/g,
                // Data loading from other sources
                /(\w+)\s*=\s*(?:load_data|read_data|get_data)\s*\(/g
            ];
            
            patterns.forEach(pattern => {
                let match;
                while ((match = pattern.exec(code)) !== null) {
                    const varName = match[1];
                    const method = match[2] || 'DataFrame';
                    const source = match[3] || 'unknown';
                    
                    dataInfo.dataframes.push({
                        variable: varName,
                        method: method,
                        source: source,
                        cellIndex: index,
                        code: match[0]
                    });
                }
            });
            
            // Look for other data variables
            const varPatterns = [
                /(\w+)\s*=\s*np\.array\s*\(/g,
                /(\w+)\s*=\s*\[.*\]/g,
                /(\w+)\s*=\s*\{.*\}/g
            ];
            
            varPatterns.forEach(pattern => {
                let match;
                while ((match = pattern.exec(code)) !== null) {
                    dataInfo.variables.push({
                        variable: match[1],
                        type: 'array/list/dict',
                        cellIndex: index,
                        code: match[0]
                    });
                }
            });
        });
    }
    
    /**
     * Detect other variables that might contain data
     */
    function detectVariables(dataInfo) {
        // Look for output cells that show DataFrame info
        const outputCells = document.querySelectorAll('.output-cell, [data-testid="output-cell"]');
        
        outputCells.forEach(cell => {
            const text = cell.textContent || '';
            
            // Look for DataFrame.info() output
            if (text.includes('RangeIndex') || text.includes('DataFrame')) {
                const lines = text.split('\n');
                lines.forEach(line => {
                    if (line.includes('entries') && line.includes('columns')) {
                        // Extract shape information
                        const shapeMatch = line.match(/(\d+)\s+entries.*?(\d+)\s+columns/);
                        if (shapeMatch) {
                            dataInfo.variables.push({
                                type: 'dataframe_info',
                                shape: [parseInt(shapeMatch[1]), parseInt(shapeMatch[2])],
                                source: 'output_analysis'
                            });
                        }
                    }
                });
            }
        });
    }
    
    /**
     * Get notebook metadata
     */
    function getNotebookMetadata(dataInfo) {
        // Get notebook title
        const titleElement = document.querySelector('h1, .notebook-title, [data-testid="notebook-title"]');
        if (titleElement) {
            dataInfo.notebooks.push({
                title: titleElement.textContent?.trim(),
                url: window.location.href
            });
        }
        
        // Get kernel information
        const kernelElement = document.querySelector('.kernel-info, [data-testid="kernel-info"]');
        if (kernelElement) {
            dataInfo.notebooks[0] = dataInfo.notebooks[0] || {};
            dataInfo.notebooks[0].kernel = kernelElement.textContent?.trim();
        }
    }
    
    /**
     * Extract filename from element
     */
    function extractFileName(element) {
        // Try different methods to get filename
        return element.textContent?.trim() ||
               element.getAttribute('title') ||
               element.getAttribute('data-filename') ||
               element.querySelector('.filename, .file-name')?.textContent?.trim() ||
               '';
    }
    
    /**
     * Check if file is a data file
     */
    function isDataFile(filename) {
        const dataExtensions = ['.csv', '.xlsx', '.xls', '.json', '.parquet', '.tsv', '.txt', '.h5', '.hdf5'];
        return dataExtensions.some(ext => filename.toLowerCase().endsWith(ext));
    }
    
    /**
     * Get file type from filename
     */
    function getFileType(filename) {
        const extension = filename.split('.').pop()?.toLowerCase();
        const typeMap = {
            'csv': 'csv',
            'xlsx': 'excel',
            'xls': 'excel',
            'json': 'json',
            'parquet': 'parquet',
            'tsv': 'csv',
            'txt': 'text',
            'h5': 'hdf5',
            'hdf5': 'hdf5'
        };
        return typeMap[extension] || 'unknown';
    }
    
    /**
     * Get element information for debugging
     */
    function getElementInfo(element) {
        return {
            tagName: element.tagName,
            className: element.className,
            id: element.id,
            textContent: element.textContent?.substring(0, 100)
        };
    }
    
    // Return the detection results
    return detectKaggleData();
})();
