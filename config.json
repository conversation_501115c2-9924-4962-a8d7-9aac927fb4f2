{"name": "Kaggle Data Analysis Assistant Configuration", "version": "1.0.0", "description": "Configuration file for the Kaggle Data Analysis Assistant", "services": {"analysis_engine": {"url": "http://localhost:8000", "description": "Local Python FastAPI analysis engine", "endpoints": {"health": "/health", "analyze": "/analyze", "analyze_file": "/analyze-file", "templates": "/templates", "supported_formats": "/supported-formats"}}, "n8n_webhook": {"url": "https://sabryroby.app.n8n.cloud/webhook-test/kaggle-analysis", "description": "n8n cloud webhook for analysis processing", "method": "POST", "timeout": 45000}}, "extension": {"default_settings": {"use_n8n": true, "auto_detect": true, "default_analysis_type": "eda", "enable_sampling": true, "sampling_threshold": 100000}, "supported_file_types": ["csv", "xlsx", "xls", "json", "parquet", "tsv"], "max_file_size_mb": 100}, "analysis": {"types": {"eda": {"name": "Exploratory Data Analysis", "description": "Comprehensive data exploration and basic statistics", "estimated_time_minutes": "15-25"}, "visualization": {"name": "Data Visualization", "description": "Charts, plots, and interactive visualizations", "estimated_time_minutes": "10-20"}, "ml": {"name": "Machine Learning", "description": "Predictive modeling and classification", "estimated_time_minutes": "20-35"}, "stats": {"name": "Statistical Analysis", "description": "Hypothesis testing and statistical inference", "estimated_time_minutes": "15-30"}}, "libraries": {"core": ["pandas", "numpy"], "visualization": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn", "plotly"], "ml": ["scikit-learn", "xgboost"], "stats": ["scipy", "statsmodels"]}}, "deployment": {"production": {"analysis_engine_url": "https://your-production-api.com", "n8n_webhook_url": "https://sabryroby.app.n8n.cloud/webhook-test/kaggle-analysis"}, "development": {"analysis_engine_url": "http://localhost:8000", "n8n_webhook_url": "https://sabryroby.app.n8n.cloud/webhook-test/kaggle-analysis"}}, "monitoring": {"enable_analytics": true, "log_requests": true, "performance_tracking": true}}