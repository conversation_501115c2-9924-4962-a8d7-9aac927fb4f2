#!/usr/bin/env python3
"""
Test script to verify the Chrome extension is working properly
"""

import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import os

def setup_chrome_with_extension():
    """Setup Chrome with the extension loaded"""
    
    # Get extension path
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(script_dir)
    extension_path = os.path.join(project_dir, 'extension')
    
    print(f"Extension path: {extension_path}")
    
    # Chrome options
    chrome_options = Options()
    chrome_options.add_argument(f"--load-extension={extension_path}")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    # Optional: Run in headless mode (comment out to see the browser)
    # chrome_options.add_argument("--headless")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        print("✅ Chrome started with extension loaded")
        return driver
    except Exception as e:
        print(f"❌ Failed to start Chrome: {e}")
        print("Make sure ChromeDriver is installed and in PATH")
        return None

def test_extension_on_kaggle(driver):
    """Test the extension on a Kaggle page"""
    
    try:
        # Go to a Kaggle notebook
        print("🔍 Navigating to Kaggle...")
        driver.get("https://www.kaggle.com/code")
        
        # Wait for page to load
        WebDriverWait(driver, 10).wait(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        print("✅ Kaggle page loaded")
        
        # Check if extension is loaded by looking for the extension icon
        # This is a basic check - in a real test you'd check for specific extension elements
        
        # Wait a bit for extension to initialize
        time.sleep(3)
        
        # Check browser console for extension messages
        logs = driver.get_log('browser')
        extension_logs = [log for log in logs if 'Kaggle Data Analysis Assistant' in log.get('message', '')]
        
        if extension_logs:
            print("✅ Extension is active (found console messages)")
            for log in extension_logs[:3]:  # Show first 3 logs
                print(f"   Log: {log['message']}")
        else:
            print("⚠️  No extension console messages found")
        
        # Try to find extension UI elements (if any are injected)
        try:
            # Look for any elements that might be added by the extension
            extension_elements = driver.find_elements(By.CSS_SELECTOR, "[data-kaggle-assistant]")
            if extension_elements:
                print(f"✅ Found {len(extension_elements)} extension UI elements")
            else:
                print("ℹ️  No extension UI elements found (this is normal if not on a notebook page)")
        except Exception as e:
            print(f"ℹ️  Could not check for extension elements: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing on Kaggle: {e}")
        return False

def test_extension_popup(driver):
    """Test the extension popup (if possible)"""
    
    try:
        # This is tricky with Selenium as extension popups are not easily accessible
        # We'll just check if the extension is loaded in the extensions page
        
        print("🔍 Checking extension in chrome://extensions/...")
        driver.get("chrome://extensions/")
        
        # Wait for page to load
        time.sleep(2)
        
        # Look for our extension
        page_source = driver.page_source.lower()
        if "kaggle data analysis assistant" in page_source:
            print("✅ Extension found in extensions page")
            return True
        else:
            print("❌ Extension not found in extensions page")
            return False
            
    except Exception as e:
        print(f"❌ Error checking extension popup: {e}")
        return False

def main():
    """Main test function"""
    
    print("🧪 Chrome Extension Test")
    print("=" * 40)
    
    # Check if extension files exist
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(script_dir)
    extension_path = os.path.join(project_dir, 'extension')
    manifest_path = os.path.join(extension_path, 'manifest.json')
    
    if not os.path.exists(manifest_path):
        print(f"❌ Extension manifest not found at: {manifest_path}")
        return 1
    
    print(f"✅ Extension files found at: {extension_path}")
    
    # Setup Chrome with extension
    driver = setup_chrome_with_extension()
    if not driver:
        return 1
    
    try:
        # Run tests
        tests = [
            ("Extension on Kaggle", lambda: test_extension_on_kaggle(driver)),
            ("Extension in Extensions Page", lambda: test_extension_popup(driver)),
        ]
        
        results = {}
        for test_name, test_func in tests:
            print(f"\n📋 Running: {test_name}")
            print("-" * 30)
            results[test_name] = test_func()
        
        # Summary
        print("\n" + "=" * 40)
        print("📊 TEST RESULTS")
        print("=" * 40)
        
        passed = 0
        total = len(results)
        
        for test_name, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{test_name:<30} {status}")
            if success:
                passed += 1
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("\n🎉 All tests passed! Extension is working.")
        else:
            print("\n⚠️  Some tests failed. Check the extension setup.")
        
        # Keep browser open for manual inspection
        input("\nPress Enter to close the browser...")
        
        return 0 if passed == total else 1
        
    finally:
        driver.quit()

if __name__ == "__main__":
    try:
        import sys
        sys.exit(main())
    except ImportError:
        print("❌ This test requires Selenium WebDriver")
        print("Install with: pip install selenium")
        print("\nAlternatively, test the extension manually:")
        print("1. Load the extension in Chrome")
        print("2. Go to a Kaggle notebook")
        print("3. Check browser console for errors")
        print("4. Try clicking the extension icon")
