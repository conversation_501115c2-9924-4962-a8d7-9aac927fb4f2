{"name": "Kaggle Analysis Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "kaggle-analysis", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "kaggle-analysis"}, {"parameters": {"functionCode": "// Validate and process incoming request\nconst payload = $json.payload || $json;\n\n// Validate required fields\nif (!payload.data_info) {\n  return [{\n    json: {\n      error: \"Missing data_info in request\",\n      status: 400\n    }\n  }];\n}\n\nif (!payload.analysis_type) {\n  payload.analysis_type = 'eda'; // Default to EDA\n}\n\n// Add metadata\npayload.request_id = $json.request_id || Date.now().toString();\npayload.timestamp = new Date().toISOString();\npayload.source = $json.source || 'unknown';\n\n// Log request\nconsole.log('Processing analysis request:', {\n  request_id: payload.request_id,\n  analysis_type: payload.analysis_type,\n  data_source: payload.data_source,\n  timestamp: payload.timestamp\n});\n\nreturn [{ json: payload }];"}, "id": "validate-request", "name": "Validate Request", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.error}}", "operation": "isEmpty"}]}}, "id": "check-validation", "name": "Check Validation", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"url": "={{$env.ANALYSIS_ENGINE_URL || 'http://localhost:8000'}}/analyze", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Request-ID", "value": "={{$json.request_id}}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "data_info", "value": "={{$json.data_info}}"}, {"name": "analysis_type", "value": "={{$json.analysis_type}}"}, {"name": "user_request", "value": "={{$json.user_request}}"}, {"name": "data_source", "value": "={{$json.data_source}}"}, {"name": "file_type", "value": "={{$json.file_type}}"}, {"name": "constraints", "value": "={{$json.constraints}}"}]}, "options": {"timeout": 30000, "retry": {"enabled": true, "maxRetries": 2}}}, "id": "call-analysis-engine", "name": "Call Analysis Engine", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [900, 200]}, {"parameters": {"functionCode": "// Process successful analysis response\nconst response = $json;\nconst originalRequest = $node[\"Validate Request\"].json;\n\n// Add metadata to response\nresponse.request_id = originalRequest.request_id;\nresponse.processing_time = Date.now() - parseInt(originalRequest.request_id);\nresponse.timestamp = new Date().toISOString();\nresponse.success = true;\n\n// Log successful analysis\nconsole.log('Analysis completed successfully:', {\n  request_id: response.request_id,\n  analysis_type: originalRequest.analysis_type,\n  processing_time: response.processing_time\n});\n\nreturn [{ json: response }];"}, "id": "process-success", "name": "Process Success", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"functionCode": "// Handle analysis engine errors\nconst error = $json;\nconst originalRequest = $node[\"Validate Request\"].json;\n\nconst errorResponse = {\n  success: false,\n  error: error.message || 'Analysis engine error',\n  request_id: originalRequest.request_id,\n  timestamp: new Date().toISOString(),\n  details: {\n    status: error.status,\n    statusText: error.statusText\n  }\n};\n\n// Log error\nconsole.error('Analysis engine error:', {\n  request_id: originalRequest.request_id,\n  error: errorResponse.error,\n  status: error.status\n});\n\nreturn [{ json: errorResponse }];"}, "id": "handle-engine-error", "name": "Handle Engine Error", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1120, 320]}, {"parameters": {"functionCode": "// Handle validation errors\nconst validationError = $json;\n\nconst errorResponse = {\n  success: false,\n  error: validationError.error || 'Validation failed',\n  status: validationError.status || 400,\n  timestamp: new Date().toISOString()\n};\n\n// Log validation error\nconsole.error('Request validation failed:', errorResponse);\n\nreturn [{ json: errorResponse }];"}, "id": "handle-validation-error", "name": "Handle Validation Error", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{$json}}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.webhookResponse", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"functionCode": "// Store analysis request for analytics\nconst data = $json;\n\n// Prepare analytics data\nconst analyticsData = {\n  request_id: data.request_id,\n  analysis_type: data.analysis_type || 'unknown',\n  data_source: data.data_source || 'unknown',\n  success: data.success || false,\n  timestamp: data.timestamp,\n  processing_time: data.processing_time || null,\n  error: data.error || null\n};\n\n// In a real implementation, you would store this in a database\n// For now, just log it\nconsole.log('Analytics data:', analyticsData);\n\nreturn [{ json: data }];"}, "id": "store-analytics", "name": "Store Analytics", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1340, 180]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Validate Request", "type": "main", "index": 0}]]}, "Validate Request": {"main": [[{"node": "Check Validation", "type": "main", "index": 0}]]}, "Check Validation": {"main": [[{"node": "Call Analysis Engine", "type": "main", "index": 0}], [{"node": "Handle Validation Error", "type": "main", "index": 0}]]}, "Call Analysis Engine": {"main": [[{"node": "Process Success", "type": "main", "index": 0}]], "error": [[{"node": "Handle Engine Error", "type": "main", "index": 0}]]}, "Process Success": {"main": [[{"node": "Store Analytics", "type": "main", "index": 0}]]}, "Handle Engine Error": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}, "Handle Validation Error": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}, "Store Analytics": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "UTC"}, "versionId": "1"}