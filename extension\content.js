/**
 * Content script for Kaggle Data Analysis Assistant
 * Integrates with Kaggle Notebook interface
 */

// Global state
let isKaggleNotebook = false;
let detectedData = null;
let assistantUI = null;

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}

/**
 * Initialize the assistant
 */
function initialize() {
  console.log('Kaggle Data Analysis Assistant: Initializing...');
  
  // Check if we're on a Kaggle notebook page
  isKaggleNotebook = detectKaggleNotebook();
  
  if (isKaggleNotebook) {
    console.log('Kaggle notebook detected');
    
    // Set up the assistant UI
    setupAssistantUI();
    
    // Start monitoring for data
    startDataMonitoring();
    
    // Listen for notebook changes
    observeNotebookChanges();
  }
}

/**
 * Detect if current page is a Kaggle notebook
 */
function detectKaggleNotebook() {
  const url = window.location.href;
  const isNotebook = url.includes('/code/') || url.includes('/notebook/');
  const hasNotebookElements = document.querySelector('.notebook-container') || 
                             document.querySelector('[data-testid="notebook"]') ||
                             document.querySelector('.kaggle-notebook');
  
  return isNotebook && hasNotebookElements;
}

/**
 * Setup the assistant UI overlay
 */
function setupAssistantUI() {
  // Create floating assistant button
  const assistantButton = document.createElement('div');
  assistantButton.id = 'kaggle-assistant-button';
  assistantButton.innerHTML = `
    <div class="assistant-icon">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
    <span class="assistant-text">Analyze Data</span>
  `;
  
  assistantButton.addEventListener('click', showAssistantPanel);
  document.body.appendChild(assistantButton);
  
  // Create assistant panel
  createAssistantPanel();
}

/**
 * Create the main assistant panel
 */
function createAssistantPanel() {
  const panel = document.createElement('div');
  panel.id = 'kaggle-assistant-panel';
  panel.innerHTML = `
    <div class="assistant-header">
      <h3>Data Analysis Assistant</h3>
      <button class="close-btn" onclick="hideAssistantPanel()">×</button>
    </div>
    
    <div class="assistant-content">
      <div class="data-detection-section">
        <h4>Detected Data</h4>
        <div id="detected-data-list">
          <p class="no-data">No data detected. Upload or reference data files in your notebook.</p>
        </div>
        <button id="refresh-data-btn" onclick="refreshDataDetection()">Refresh Detection</button>
      </div>
      
      <div class="analysis-options-section">
        <h4>Analysis Type</h4>
        <select id="analysis-type-select">
          <option value="eda">Exploratory Data Analysis</option>
          <option value="visualization">Data Visualization</option>
          <option value="ml">Machine Learning</option>
          <option value="stats">Statistical Analysis</option>
        </select>
      </div>
      
      <div class="user-request-section">
        <h4>Specific Request (Optional)</h4>
        <textarea id="user-request-input" placeholder="Describe what you want to analyze or any specific questions..."></textarea>
      </div>
      
      <div class="action-buttons">
        <button id="analyze-btn" onclick="startAnalysis()" disabled>Generate Analysis</button>
        <button id="settings-btn" onclick="showSettings()">Settings</button>
      </div>
      
      <div id="analysis-results" class="results-section" style="display: none;">
        <h4>Analysis Results</h4>
        <div class="results-content">
          <div class="plan-section">
            <h5>Analysis Plan</h5>
            <pre id="analysis-plan"></pre>
          </div>
          <div class="code-section">
            <h5>Generated Code</h5>
            <pre id="generated-code"></pre>
            <button id="copy-code-btn" onclick="copyCodeToClipboard()">Copy Code</button>
            <button id="insert-code-btn" onclick="insertCodeToNotebook()">Insert to Notebook</button>
          </div>
        </div>
      </div>
      
      <div id="loading-indicator" class="loading" style="display: none;">
        <div class="spinner"></div>
        <p>Generating analysis...</p>
      </div>
    </div>
  `;
  
  document.body.appendChild(panel);
  assistantUI = panel;
}

/**
 * Show the assistant panel
 */
function showAssistantPanel() {
  if (assistantUI) {
    assistantUI.style.display = 'block';
    refreshDataDetection();
  }
}

/**
 * Hide the assistant panel
 */
function hideAssistantPanel() {
  if (assistantUI) {
    assistantUI.style.display = 'none';
  }
}

/**
 * Start monitoring for data in the notebook
 */
function startDataMonitoring() {
  // Initial detection
  detectNotebookData();
  
  // Monitor for changes every 5 seconds
  setInterval(detectNotebookData, 5000);
}

/**
 * Detect data in the current notebook
 */
function detectNotebookData() {
  const dataInfo = {
    files: [],
    dataframes: [],
    variables: []
  };
  
  // Look for file uploads and references
  const fileElements = document.querySelectorAll('[data-testid="file-item"], .file-item, .input-file');
  fileElements.forEach(element => {
    const fileName = element.textContent || element.getAttribute('title') || '';
    if (fileName.match(/\.(csv|xlsx?|json|parquet)$/i)) {
      dataInfo.files.push({
        name: fileName,
        type: fileName.split('.').pop().toLowerCase(),
        element: element
      });
    }
  });
  
  // Look for pandas DataFrame references in code cells
  const codeCells = document.querySelectorAll('.code-cell, [data-testid="code-cell"]');
  codeCells.forEach(cell => {
    const code = cell.textContent || '';
    
    // Look for DataFrame creation patterns
    const dfPatterns = [
      /(\w+)\s*=\s*pd\.read_csv\s*\(\s*['"]([^'"]+)['"]/g,
      /(\w+)\s*=\s*pd\.read_excel\s*\(\s*['"]([^'"]+)['"]/g,
      /(\w+)\s*=\s*pd\.read_json\s*\(\s*['"]([^'"]+)['"]/g
    ];
    
    dfPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(code)) !== null) {
        dataInfo.dataframes.push({
          variable: match[1],
          source: match[2],
          type: 'dataframe'
        });
      }
    });
  });
  
  detectedData = dataInfo;
  updateDataDisplay();
}

/**
 * Update the data display in the panel
 */
function updateDataDisplay() {
  const dataList = document.getElementById('detected-data-list');
  if (!dataList) return;
  
  if (!detectedData || (detectedData.files.length === 0 && detectedData.dataframes.length === 0)) {
    dataList.innerHTML = '<p class="no-data">No data detected. Upload or reference data files in your notebook.</p>';
    document.getElementById('analyze-btn').disabled = true;
    return;
  }
  
  let html = '';
  
  // Display detected files
  if (detectedData.files.length > 0) {
    html += '<div class="data-category"><h5>Data Files</h5><ul>';
    detectedData.files.forEach(file => {
      html += `<li class="data-item" data-type="file" data-name="${file.name}">
        <span class="file-icon">${getFileIcon(file.type)}</span>
        <span class="file-name">${file.name}</span>
        <span class="file-type">${file.type.toUpperCase()}</span>
      </li>`;
    });
    html += '</ul></div>';
  }
  
  // Display detected DataFrames
  if (detectedData.dataframes.length > 0) {
    html += '<div class="data-category"><h5>DataFrames</h5><ul>';
    detectedData.dataframes.forEach(df => {
      html += `<li class="data-item" data-type="dataframe" data-name="${df.variable}">
        <span class="df-icon">📊</span>
        <span class="df-name">${df.variable}</span>
        <span class="df-source">${df.source}</span>
      </li>`;
    });
    html += '</ul></div>';
  }
  
  dataList.innerHTML = html;
  document.getElementById('analyze-btn').disabled = false;
  
  // Add click handlers for data items
  document.querySelectorAll('.data-item').forEach(item => {
    item.addEventListener('click', () => selectDataItem(item));
  });
}

/**
 * Get file icon based on type
 */
function getFileIcon(type) {
  const icons = {
    'csv': '📄',
    'xlsx': '📊',
    'xls': '📊',
    'json': '📋',
    'parquet': '🗃️'
  };
  return icons[type] || '📄';
}

/**
 * Select a data item for analysis
 */
function selectDataItem(item) {
  // Remove previous selections
  document.querySelectorAll('.data-item').forEach(el => el.classList.remove('selected'));
  
  // Select current item
  item.classList.add('selected');
}

/**
 * Refresh data detection
 */
function refreshDataDetection() {
  detectNotebookData();
}

/**
 * Start the analysis process
 */
async function startAnalysis() {
  const selectedItem = document.querySelector('.data-item.selected');
  if (!selectedItem) {
    alert('Please select a data source first');
    return;
  }
  
  const analysisType = document.getElementById('analysis-type-select').value;
  const userRequest = document.getElementById('user-request-input').value;
  
  // Show loading
  document.getElementById('loading-indicator').style.display = 'block';
  document.getElementById('analysis-results').style.display = 'none';
  
  try {
    // Prepare analysis request
    const analysisData = {
      dataInfo: await extractDataInfo(selectedItem),
      analysisType: analysisType,
      userRequest: userRequest || null,
      dataSource: selectedItem.dataset.type,
      fileType: getFileTypeFromItem(selectedItem)
    };
    
    // Send to background script
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        action: 'analyzeData',
        data: analysisData
      }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }

        if (response && response.success) {
          resolve(response.data);
        } else {
          reject(new Error(response?.error || 'Unknown error occurred'));
        }
      });
    });
    
    // Display results
    displayAnalysisResults(response);
    
  } catch (error) {
    console.error('Analysis failed:', error);
    alert(`Analysis failed: ${error.message}`);
  } finally {
    document.getElementById('loading-indicator').style.display = 'none';
  }
}

/**
 * Extract data information from selected item
 */
async function extractDataInfo(item) {
  // This would extract actual data structure information
  // For now, return mock data structure
  return {
    columns: ['column1', 'column2', 'column3'],
    dtypes: {'column1': 'int64', 'column2': 'object', 'column3': 'float64'},
    shape: [1000, 3],
    sample_data: [
      {'column1': 1, 'column2': 'value1', 'column3': 1.5},
      {'column1': 2, 'column2': 'value2', 'column3': 2.5}
    ]
  };
}

/**
 * Get file type from selected item
 */
function getFileTypeFromItem(item) {
  if (item.dataset.type === 'file') {
    return item.dataset.name.split('.').pop().toLowerCase();
  }
  return 'csv'; // Default assumption for DataFrames
}

/**
 * Display analysis results
 */
function displayAnalysisResults(results) {
  document.getElementById('analysis-plan').textContent = results.plan;
  document.getElementById('generated-code').textContent = results.code;
  document.getElementById('analysis-results').style.display = 'block';
}

/**
 * Copy generated code to clipboard
 */
function copyCodeToClipboard() {
  const code = document.getElementById('generated-code').textContent;
  navigator.clipboard.writeText(code).then(() => {
    alert('Code copied to clipboard!');
  });
}

/**
 * Insert code into notebook (placeholder)
 */
function insertCodeToNotebook() {
  alert('Code insertion feature coming soon!');
  // TODO: Implement actual code insertion into Kaggle notebook
}

/**
 * Observe notebook changes
 */
function observeNotebookChanges() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        // Check for new code cells or file uploads
        detectNotebookData();
      }
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

// Make functions globally available
window.hideAssistantPanel = hideAssistantPanel;
window.refreshDataDetection = refreshDataDetection;
window.startAnalysis = startAnalysis;
window.copyCodeToClipboard = copyCodeToClipboard;
window.insertCodeToNotebook = insertCodeToNotebook;
