"""
Core data analyzer for understanding data structure and characteristics
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List
import json
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class DataAnalyzer:
    """Main data analyzer class"""
    
    def __init__(self):
        self.supported_formats = ['csv', 'excel', 'xlsx', 'xls', 'json']
        self.sampling_threshold = 100000  # Rows
        self.max_file_size_mb = 100
    
    def analyze_data_structure(
        self, 
        data_info: Dict[str, Any], 
        file_type: Optional[str] = None,
        constraints: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Analyze data structure from provided metadata
        
        Args:
            data_info: Dictionary containing data information
            file_type: Type of data file
            constraints: Any constraints or limitations
            
        Returns:
            Analysis context dictionary
        """
        try:
            context = {
                "file_type": file_type,
                "constraints": constraints or {},
                "analysis_timestamp": pd.Timestamp.now().isoformat()
            }
            
            # Extract basic information
            if "columns" in data_info:
                context["columns"] = data_info["columns"]
                context["column_count"] = len(data_info["columns"])
                
            if "dtypes" in data_info:
                context["dtypes"] = data_info["dtypes"]
                context["data_types"] = self._categorize_data_types(data_info["dtypes"])
                
            if "shape" in data_info:
                context["data_shape"] = data_info["shape"]
                context["row_count"] = data_info["shape"][0] if len(data_info["shape"]) > 0 else 0
                
            # Determine if sampling is needed
            if context.get("row_count", 0) > self.sampling_threshold:
                context["suggested_sampling"] = {
                    "needed": True,
                    "strategy": "random",
                    "sample_size": min(50000, context["row_count"] // 2),
                    "reason": f"Dataset has {context['row_count']} rows, exceeding threshold of {self.sampling_threshold}"
                }
            else:
                context["suggested_sampling"] = {"needed": False}
                
            # Analyze data characteristics
            if "sample_data" in data_info:
                context.update(self._analyze_sample_data(data_info["sample_data"]))
                
            # Suggest analysis approaches
            context["suggested_analyses"] = self._suggest_analyses(context)
            
            return context
            
        except Exception as e:
            logger.error(f"Error analyzing data structure: {str(e)}")
            raise
    
    def extract_file_info(self, file_path: str, filename: str) -> Dict[str, Any]:
        """
        Extract information from an actual data file
        
        Args:
            file_path: Path to the data file
            filename: Original filename
            
        Returns:
            Dictionary with extracted data information
        """
        try:
            file_ext = Path(filename).suffix.lower().replace('.', '')
            
            if file_ext == 'csv':
                return self._extract_csv_info(file_path)
            elif file_ext in ['xlsx', 'xls', 'excel']:
                return self._extract_excel_info(file_path)
            elif file_ext == 'json':
                return self._extract_json_info(file_path)
            else:
                raise ValueError(f"Unsupported file format: {file_ext}")
                
        except Exception as e:
            logger.error(f"Error extracting file info: {str(e)}")
            raise
    
    def _extract_csv_info(self, file_path: str) -> Dict[str, Any]:
        """Extract information from CSV file"""
        try:
            # Read sample to get structure
            df_sample = pd.read_csv(file_path, nrows=1000)
            
            # Get full shape (more efficient)
            with open(file_path, 'r') as f:
                total_rows = sum(1 for _ in f) - 1  # Subtract header
            
            return {
                "columns": df_sample.columns.tolist(),
                "dtypes": df_sample.dtypes.astype(str).to_dict(),
                "shape": (total_rows, len(df_sample.columns)),
                "sample_data": df_sample.head(10).to_dict('records'),
                "missing_values": df_sample.isnull().sum().to_dict(),
                "file_type": "csv"
            }
            
        except Exception as e:
            logger.error(f"Error reading CSV file: {str(e)}")
            raise
    
    def _extract_excel_info(self, file_path: str) -> Dict[str, Any]:
        """Extract information from Excel file"""
        try:
            # Read sample
            df_sample = pd.read_excel(file_path, nrows=1000)
            
            # Get sheet names
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            return {
                "columns": df_sample.columns.tolist(),
                "dtypes": df_sample.dtypes.astype(str).to_dict(),
                "shape": df_sample.shape,  # Approximate from sample
                "sample_data": df_sample.head(10).to_dict('records'),
                "missing_values": df_sample.isnull().sum().to_dict(),
                "sheet_names": sheet_names,
                "file_type": "excel"
            }
            
        except Exception as e:
            logger.error(f"Error reading Excel file: {str(e)}")
            raise
    
    def _extract_json_info(self, file_path: str) -> Dict[str, Any]:
        """Extract information from JSON file"""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            # Convert to DataFrame if possible
            if isinstance(data, list) and len(data) > 0:
                df_sample = pd.DataFrame(data[:1000])
                
                return {
                    "columns": df_sample.columns.tolist(),
                    "dtypes": df_sample.dtypes.astype(str).to_dict(),
                    "shape": (len(data), len(df_sample.columns)),
                    "sample_data": df_sample.head(10).to_dict('records'),
                    "missing_values": df_sample.isnull().sum().to_dict(),
                    "file_type": "json"
                }
            else:
                # Handle nested JSON structure
                return {
                    "structure": str(type(data)),
                    "sample_data": data if len(str(data)) < 1000 else str(data)[:1000],
                    "file_type": "json",
                    "note": "Complex JSON structure - manual inspection recommended"
                }
                
        except Exception as e:
            logger.error(f"Error reading JSON file: {str(e)}")
            raise
    
    def _categorize_data_types(self, dtypes: Dict[str, str]) -> Dict[str, List[str]]:
        """Categorize columns by data type"""
        categories = {
            "numeric": [],
            "categorical": [],
            "datetime": [],
            "text": [],
            "boolean": []
        }
        
        for col, dtype in dtypes.items():
            dtype_lower = dtype.lower()
            
            if any(x in dtype_lower for x in ['int', 'float', 'number']):
                categories["numeric"].append(col)
            elif any(x in dtype_lower for x in ['datetime', 'timestamp', 'date']):
                categories["datetime"].append(col)
            elif any(x in dtype_lower for x in ['bool']):
                categories["boolean"].append(col)
            elif any(x in dtype_lower for x in ['object', 'string', 'category']):
                categories["categorical"].append(col)
            else:
                categories["text"].append(col)
                
        return categories
    
    def _analyze_sample_data(self, sample_data: List[Dict]) -> Dict[str, Any]:
        """Analyze sample data for patterns and characteristics"""
        if not sample_data:
            return {}
        
        df_sample = pd.DataFrame(sample_data)
        
        analysis = {
            "sample_size": len(df_sample),
            "unique_counts": {},
            "value_ranges": {},
            "potential_targets": []
        }
        
        for col in df_sample.columns:
            # Unique value counts
            unique_count = df_sample[col].nunique()
            analysis["unique_counts"][col] = unique_count
            
            # Identify potential target variables
            if unique_count <= 10 and df_sample[col].dtype in ['object', 'category', 'bool']:
                analysis["potential_targets"].append(col)
            
            # Value ranges for numeric columns
            if pd.api.types.is_numeric_dtype(df_sample[col]):
                analysis["value_ranges"][col] = {
                    "min": float(df_sample[col].min()),
                    "max": float(df_sample[col].max()),
                    "mean": float(df_sample[col].mean())
                }
        
        return analysis
    
    def _suggest_analyses(self, context: Dict[str, Any]) -> List[str]:
        """Suggest appropriate analysis types based on data characteristics"""
        suggestions = []
        
        data_types = context.get("data_types", {})
        
        # Always suggest EDA
        suggestions.append("eda")
        
        # Suggest visualization if we have good columns for plotting
        if data_types.get("numeric") or data_types.get("categorical"):
            suggestions.append("visualization")
        
        # Suggest ML if we have potential targets and features
        if (context.get("potential_targets") and 
            len(data_types.get("numeric", [])) > 1):
            suggestions.append("ml")
        
        # Suggest stats if we have numeric data
        if len(data_types.get("numeric", [])) >= 2:
            suggestions.append("stats")
        
        return suggestions
