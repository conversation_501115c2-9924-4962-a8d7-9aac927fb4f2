"""
Kaggle Data Analysis Assistant - Chat App
A conversational interface for data analysis assistance
"""

import streamlit as st
import requests
import json
import time
from datetime import datetime
import pandas as pd
import io
import base64

# Configuration
ANALYSIS_ENGINE_URL = "http://localhost:8000"
N8N_WEBHOOK_URL = "https://sabryroby.app.n8n.cloud/webhook-test/kaggle-analysis"

# Page configuration
st.set_page_config(
    page_title="Kaggle Data Analysis Assistant",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for chat interface
st.markdown("""
<style>
.chat-message {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
}
.user-message {
    background-color: #e3f2fd;
    margin-left: 20%;
}
.assistant-message {
    background-color: #f5f5f5;
    margin-right: 20%;
}
.message-header {
    font-weight: bold;
    margin-bottom: 0.5rem;
}
.code-block {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 1rem;
    margin: 0.5rem 0;
    font-family: 'Courier New', monospace;
    white-space: pre-wrap;
}
.analysis-plan {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 0.25rem;
    padding: 1rem;
    margin: 0.5rem 0;
}
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables"""
    if "messages" not in st.session_state:
        st.session_state.messages = [
            {
                "role": "assistant",
                "content": "👋 Hi! I'm your Kaggle Data Analysis Assistant. I can help you analyze data, create visualizations, build ML models, and perform statistical analysis.\n\n**How can I help you today?**\n\n💡 You can:\n- Upload a dataset and ask for analysis\n- Describe your data and get analysis suggestions\n- Ask for specific analysis types (EDA, ML, visualization, stats)\n- Get Python code for data science tasks",
                "timestamp": datetime.now()
            }
        ]
    
    if "uploaded_data" not in st.session_state:
        st.session_state.uploaded_data = None
    
    if "data_info" not in st.session_state:
        st.session_state.data_info = None

def detect_data_info(df):
    """Extract data information from uploaded DataFrame"""
    data_info = {
        "columns": df.columns.tolist(),
        "dtypes": {col: str(df[col].dtype) for col in df.columns},
        "shape": list(df.shape),
        "sample_data": df.head(3).to_dict('records'),
        "missing_values": df.isnull().sum().to_dict(),
        "memory_usage": df.memory_usage(deep=True).sum()
    }
    return data_info

def send_analysis_request(user_message, data_info=None, analysis_type="eda"):
    """Send analysis request to the backend"""
    
    payload = {
        "source": "chat_app",
        "timestamp": time.time(),
        "payload": {
            "user_request": user_message,
            "analysis_type": analysis_type,
            "data_source": "chat_upload" if data_info else "description",
            "file_type": "csv"
        }
    }
    
    if data_info:
        payload["payload"]["data_info"] = data_info
    
    try:
        # Try n8n webhook first
        response = requests.post(N8N_WEBHOOK_URL, json=payload, timeout=30)
        if response.status_code == 200:
            return response.json()
        else:
            # Fallback to direct analysis engine
            response = requests.post(f"{ANALYSIS_ENGINE_URL}/analyze", 
                                   json=payload["payload"], timeout=30)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Analysis failed: {response.status_code}"}
    
    except Exception as e:
        return {"error": f"Connection error: {str(e)}"}

def determine_analysis_type(message):
    """Determine analysis type from user message"""
    message_lower = message.lower()
    
    if any(word in message_lower for word in ["machine learning", "ml", "predict", "model", "classification", "regression"]):
        return "ml"
    elif any(word in message_lower for word in ["visualiz", "plot", "chart", "graph", "dashboard"]):
        return "visualization"
    elif any(word in message_lower for word in ["statistic", "test", "hypothesis", "correlation", "significance"]):
        return "stats"
    else:
        return "eda"

def format_analysis_response(response):
    """Format the analysis response for chat display"""
    if "error" in response:
        return f"❌ **Error**: {response['error']}"
    
    formatted_response = ""
    
    if "plan" in response:
        formatted_response += f"""
### 📋 **Analysis Plan**

<div class="analysis-plan">
{response['plan']}
</div>
"""
    
    if "code" in response:
        formatted_response += f"""
### 💻 **Generated Python Code**

```python
{response['code']}
```
"""
    
    if "metadata" in response and response["metadata"]:
        metadata = response["metadata"]
        if metadata.get("suggested_sampling"):
            formatted_response += f"""
### ⚠️ **Note**: Large dataset detected. Consider sampling for better performance.
"""
    
    return formatted_response

def display_chat_message(message):
    """Display a chat message"""
    role = message["role"]
    content = message["content"]
    timestamp = message.get("timestamp", datetime.now())
    
    if role == "user":
        st.markdown(f"""
        <div class="chat-message user-message">
            <div class="message-header">👤 You - {timestamp.strftime('%H:%M')}</div>
            <div>{content}</div>
        </div>
        """, unsafe_allow_html=True)
    else:
        st.markdown(f"""
        <div class="chat-message assistant-message">
            <div class="message-header">🤖 Assistant - {timestamp.strftime('%H:%M')}</div>
            <div>{content}</div>
        </div>
        """, unsafe_allow_html=True)

def main():
    """Main chat application"""
    
    # Initialize session state
    initialize_session_state()
    
    # Sidebar
    with st.sidebar:
        st.title("📊 Data Analysis Chat")
        
        # File upload
        st.subheader("📁 Upload Data")
        uploaded_file = st.file_uploader(
            "Upload your dataset",
            type=['csv', 'xlsx', 'json'],
            help="Upload a CSV, Excel, or JSON file to analyze"
        )
        
        if uploaded_file is not None:
            try:
                # Read the file
                if uploaded_file.name.endswith('.csv'):
                    df = pd.read_csv(uploaded_file)
                elif uploaded_file.name.endswith(('.xlsx', '.xls')):
                    df = pd.read_excel(uploaded_file)
                elif uploaded_file.name.endswith('.json'):
                    df = pd.read_json(uploaded_file)
                
                st.session_state.uploaded_data = df
                st.session_state.data_info = detect_data_info(df)
                
                st.success(f"✅ Loaded {df.shape[0]} rows, {df.shape[1]} columns")
                
                # Show data preview
                with st.expander("📊 Data Preview"):
                    st.dataframe(df.head())
                    
                with st.expander("ℹ️ Data Info"):
                    st.write(f"**Shape**: {df.shape}")
                    st.write(f"**Columns**: {', '.join(df.columns.tolist())}")
                    st.write(f"**Data Types**:")
                    for col, dtype in df.dtypes.items():
                        st.write(f"  - {col}: {dtype}")
                        
            except Exception as e:
                st.error(f"Error loading file: {str(e)}")
        
        # Quick actions
        st.subheader("⚡ Quick Actions")
        
        if st.button("🔍 Exploratory Data Analysis"):
            if st.session_state.data_info:
                user_message = "Please perform an exploratory data analysis on my uploaded dataset."
                st.session_state.messages.append({
                    "role": "user",
                    "content": user_message,
                    "timestamp": datetime.now()
                })
                st.rerun()
        
        if st.button("📊 Create Visualizations"):
            if st.session_state.data_info:
                user_message = "Please create visualizations for my uploaded dataset."
                st.session_state.messages.append({
                    "role": "user", 
                    "content": user_message,
                    "timestamp": datetime.now()
                })
                st.rerun()
        
        if st.button("🤖 Build ML Model"):
            if st.session_state.data_info:
                user_message = "Please help me build a machine learning model with my uploaded dataset."
                st.session_state.messages.append({
                    "role": "user",
                    "content": user_message,
                    "timestamp": datetime.now()
                })
                st.rerun()
        
        if st.button("📈 Statistical Analysis"):
            if st.session_state.data_info:
                user_message = "Please perform statistical analysis on my uploaded dataset."
                st.session_state.messages.append({
                    "role": "user",
                    "content": user_message,
                    "timestamp": datetime.now()
                })
                st.rerun()
        
        # Settings
        st.subheader("⚙️ Settings")
        use_n8n = st.checkbox("Use n8n Backend", value=True)
        
        # Clear chat
        if st.button("🗑️ Clear Chat"):
            st.session_state.messages = st.session_state.messages[:1]  # Keep welcome message
            st.rerun()
    
    # Main chat interface
    st.title("💬 Kaggle Data Analysis Assistant")
    
    # Display chat messages
    chat_container = st.container()
    with chat_container:
        for message in st.session_state.messages:
            display_chat_message(message)
    
    # Chat input
    user_input = st.chat_input("Ask me anything about data analysis...")
    
    if user_input:
        # Add user message
        st.session_state.messages.append({
            "role": "user",
            "content": user_input,
            "timestamp": datetime.now()
        })
        
        # Determine analysis type
        analysis_type = determine_analysis_type(user_input)
        
        # Show thinking indicator
        with st.spinner("🤔 Analyzing your request..."):
            # Send analysis request
            response = send_analysis_request(
                user_input, 
                st.session_state.data_info, 
                analysis_type
            )
            
            # Format and add assistant response
            formatted_response = format_analysis_response(response)
            
            st.session_state.messages.append({
                "role": "assistant",
                "content": formatted_response,
                "timestamp": datetime.now()
            })
        
        # Rerun to update chat
        st.rerun()

if __name__ == "__main__":
    main()
