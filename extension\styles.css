/* Content script styles for Kaggle Data Analysis Assistant */

/* Assistant But<PERSON> */
#kaggle-assistant-button {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 12px 20px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    user-select: none;
}

#kaggle-assistant-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

#kaggle-assistant-button .assistant-icon {
    display: flex;
    align-items: center;
}

/* Assistant Panel */
#kaggle-assistant-panel {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    width: 400px;
    max-height: 80vh;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    z-index: 10001;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow: hidden;
    display: none;
}

/* Panel Header */
.assistant-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.assistant-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.2s;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Panel Content */
.assistant-content {
    padding: 20px;
    max-height: calc(80vh - 68px);
    overflow-y: auto;
}

.assistant-content h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
}

/* Data Detection Section */
.data-detection-section {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

#detected-data-list {
    margin-bottom: 12px;
}

.no-data {
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
    margin: 0;
}

.data-category {
    margin-bottom: 16px;
}

.data-category h5 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: #34495e;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-category ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.data-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    margin-bottom: 4px;
    cursor: pointer;
    transition: all 0.2s;
    background: white;
}

.data-item:hover {
    border-color: #3498db;
    background: #f8f9fa;
}

.data-item.selected {
    border-color: #3498db;
    background: #e3f2fd;
}

.file-icon, .df-icon {
    font-size: 16px;
}

.file-name, .df-name {
    flex: 1;
    font-weight: 500;
    font-size: 13px;
}

.file-type, .df-source {
    font-size: 11px;
    color: #7f8c8d;
    background: #ecf0f1;
    padding: 2px 6px;
    border-radius: 3px;
}

#refresh-data-btn {
    width: 100%;
    padding: 8px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

#refresh-data-btn:hover {
    background: #e9ecef;
    border-color: #3498db;
}

/* Analysis Options */
.analysis-options-section {
    margin-bottom: 20px;
}

#analysis-type-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 13px;
    background: white;
}

/* User Request */
.user-request-section {
    margin-bottom: 20px;
}

#user-request-input {
    width: 100%;
    min-height: 60px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 13px;
    font-family: inherit;
    resize: vertical;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
}

#analyze-btn {
    flex: 1;
    padding: 10px 16px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    font-size: 13px;
    transition: background 0.2s;
}

#analyze-btn:hover:not(:disabled) {
    background: #2980b9;
}

#analyze-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

#settings-btn {
    padding: 10px 16px;
    background: #95a5a6;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    transition: background 0.2s;
}

#settings-btn:hover {
    background: #7f8c8d;
}

/* Results Section */
.results-section {
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.plan-section, .code-section {
    margin-bottom: 16px;
}

.plan-section h5, .code-section h5 {
    margin: 0 0 8px 0;
    font-size: 13px;
    font-weight: 600;
    color: #2c3e50;
}

#analysis-plan, #generated-code {
    background: #f8f9fa;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    padding: 12px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 11px;
    line-height: 1.4;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
    margin: 0;
}

#generated-code {
    background: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
}

.code-section {
    position: relative;
}

#copy-code-btn, #insert-code-btn {
    margin-top: 8px;
    margin-right: 8px;
    padding: 6px 12px;
    background: #34495e;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: background 0.2s;
}

#copy-code-btn:hover, #insert-code-btn:hover {
    background: #2c3e50;
}

/* Loading Indicator */
.loading {
    text-align: center;
    padding: 40px 20px;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    margin: 0;
    color: #7f8c8d;
    font-size: 13px;
}

/* Scrollbar */
.assistant-content::-webkit-scrollbar {
    width: 6px;
}

.assistant-content::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.assistant-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.assistant-content::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #kaggle-assistant-panel {
        right: 10px;
        left: 10px;
        width: auto;
        max-width: none;
    }
    
    #kaggle-assistant-button {
        right: 10px;
    }
}
