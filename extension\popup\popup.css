/* Popup styles for Kaggle Data Analysis Assistant */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
}

.popup-container {
    width: 380px;
    max-height: 600px;
    overflow-y: auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Header */
.popup-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo h1 {
    font-size: 16px;
    font-weight: 600;
}

.version {
    font-size: 12px;
    opacity: 0.8;
}

/* Content */
.popup-content {
    padding: 16px;
}

section {
    margin-bottom: 20px;
}

h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #2c3e50;
}

/* Status Section */
.status-section {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #e74c3c;
}

.status-dot.online {
    background: #27ae60;
}

.status-dot.offline {
    background: #e74c3c;
}

.kaggle-status {
    font-size: 12px;
    color: #7f8c8d;
}

/* Quick Actions */
.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 12px 8px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 12px;
}

.action-btn:hover:not(:disabled) {
    background: #f8f9fa;
    border-color: #3498db;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-icon {
    font-size: 16px;
}

/* History Section */
.history-list {
    max-height: 120px;
    overflow-y: auto;
}

.history-item {
    padding: 8px;
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 4px;
    font-size: 12px;
}

.no-history {
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
    padding: 20px;
}

/* Settings Section */
.setting-item {
    margin-bottom: 12px;
}

.setting-item label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    font-size: 12px;
}

.setting-item input[type="text"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
}

.setting-item input[type="checkbox"] {
    margin-right: 8px;
}

.save-btn {
    width: 100%;
    padding: 10px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.2s;
}

.save-btn:hover {
    background: #2980b9;
}

/* Help Section */
.help-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.help-links a {
    color: #3498db;
    text-decoration: none;
    font-size: 12px;
    padding: 4px 0;
}

.help-links a:hover {
    text-decoration: underline;
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification */
.notification {
    position: fixed;
    top: 16px;
    left: 16px;
    right: 16px;
    background: #27ae60;
    color: white;
    padding: 12px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1001;
}

.notification.error {
    background: #e74c3c;
}

.notification.warning {
    background: #f39c12;
}

#notification-close {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}
