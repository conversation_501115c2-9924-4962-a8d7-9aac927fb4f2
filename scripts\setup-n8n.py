#!/usr/bin/env python3
"""
Setup script for configuring the Kaggle Data Analysis Assistant with n8n
"""

import json
import requests
import time
import sys
from pathlib import Path

# Your n8n webhook URL
N8N_WEBHOOK_URL = "https://sabryroby.app.n8n.cloud/webhook-test/kaggle-analysis"
ANALYSIS_ENGINE_URL = "http://localhost:8000"

def test_n8n_webhook():
    """Test the n8n webhook connectivity"""
    print("🔗 Testing n8n webhook connectivity...")
    
    test_payload = {
        "source": "setup_script",
        "timestamp": time.time(),
        "test": True,
        "payload": {
            "data_info": {
                "columns": ["test_col1", "test_col2"],
                "dtypes": {"test_col1": "int64", "test_col2": "object"},
                "shape": [100, 2]
            },
            "analysis_type": "eda",
            "data_source": "test",
            "file_type": "csv"
        }
    }
    
    try:
        print(f"Sending test request to: {N8N_WEBHOOK_URL}")
        response = requests.post(N8N_WEBHOOK_URL, json=test_payload, timeout=30)
        
        if response.status_code == 200:
            print("✅ n8n webhook is accessible")
            try:
                result = response.json()
                print(f"   Response: {result}")
                return True
            except json.JSONDecodeError:
                print(f"   Response (text): {response.text[:200]}...")
                return True
        else:
            print(f"❌ Webhook returned status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to connect to n8n webhook: {e}")
        return False

def test_analysis_engine():
    """Test the local analysis engine"""
    print("\n🔍 Testing local analysis engine...")
    
    try:
        response = requests.get(f"{ANALYSIS_ENGINE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Analysis engine is running")
            return True
        else:
            print(f"❌ Analysis engine returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException:
        print("❌ Analysis engine is not running")
        print("   Start it with: cd analysis-engine && python main.py")
        return False

def create_n8n_workflow_instructions():
    """Create instructions for setting up the n8n workflow"""
    print("\n📋 n8n Workflow Setup Instructions")
    print("=" * 50)
    
    instructions = f"""
To set up the n8n workflow on your cloud instance:

1. **Access your n8n instance:**
   - Go to: https://sabryroby.app.n8n.cloud/

2. **Import the workflow:**
   - Click "Workflows" in the sidebar
   - Click "Import from file" 
   - Upload: n8n-workflows/analysis-workflow.json

3. **Configure the webhook:**
   - The webhook should be automatically set to: {N8N_WEBHOOK_URL}
   - Make sure the webhook path is: "kaggle-analysis"

4. **Set environment variables:**
   - Go to Settings → Environment Variables
   - Add: ANALYSIS_ENGINE_URL = {ANALYSIS_ENGINE_URL}
   - (Or your production analysis engine URL)

5. **Activate the workflow:**
   - Click the workflow to open it
   - Click "Active" toggle to enable it

6. **Test the workflow:**
   - Use the test button in n8n
   - Or run: python scripts/test-system.py

The workflow will:
- Receive requests from the Chrome extension
- Validate the request data
- Call your analysis engine
- Return formatted results
"""
    
    print(instructions)
    
    # Save instructions to file
    with open("n8n-setup-instructions.txt", "w", encoding="utf-8") as f:
        f.write(instructions)
    
    print("📄 Instructions saved to: n8n-setup-instructions.txt")

def update_extension_config():
    """Update extension configuration files"""
    print("\n⚙️  Updating extension configuration...")
    
    # Update manifest.json host permissions
    manifest_path = Path("extension/manifest.json")
    if manifest_path.exists():
        with open(manifest_path, 'r') as f:
            manifest = json.load(f)
        
        # Add n8n domain to host permissions
        n8n_domain = "https://sabryroby.app.n8n.cloud/*"
        if n8n_domain not in manifest.get("host_permissions", []):
            manifest.setdefault("host_permissions", []).append(n8n_domain)
        
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)
        
        print("✅ Updated extension manifest.json")
    
    print(f"✅ Extension configured to use n8n webhook: {N8N_WEBHOOK_URL}")

def create_test_request():
    """Create a comprehensive test request"""
    print("\n🧪 Creating comprehensive test...")
    
    test_data = {
        "source": "setup_validation",
        "timestamp": time.time(),
        "payload": {
            "data_info": {
                "columns": ["PassengerId", "Survived", "Pclass", "Name", "Sex", "Age", "Fare"],
                "dtypes": {
                    "PassengerId": "int64",
                    "Survived": "int64", 
                    "Pclass": "int64",
                    "Name": "object",
                    "Sex": "object",
                    "Age": "float64",
                    "Fare": "float64"
                },
                "shape": [891, 7],
                "sample_data": [
                    {
                        "PassengerId": 1, "Survived": 0, "Pclass": 3, 
                        "Name": "Braund, Mr. Owen Harris", "Sex": "male", 
                        "Age": 22.0, "Fare": 7.25
                    }
                ]
            },
            "analysis_type": "eda",
            "user_request": "Analyze passenger survival patterns",
            "data_source": "file",
            "file_type": "csv"
        }
    }
    
    try:
        print("Sending comprehensive test to n8n...")
        response = requests.post(N8N_WEBHOOK_URL, json=test_data, timeout=45)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success") and "plan" in result and "code" in result:
                print("✅ Full analysis workflow successful!")
                print(f"   Plan length: {len(result['plan'])} characters")
                print(f"   Code length: {len(result['code'])} characters")
                return True
            else:
                print("❌ Workflow completed but response format is incorrect")
                print(f"   Response: {result}")
                return False
        else:
            print(f"❌ Workflow failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Test request failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Kaggle Data Analysis Assistant - n8n Setup")
    print("=" * 60)
    print(f"n8n Webhook URL: {N8N_WEBHOOK_URL}")
    print(f"Analysis Engine: {ANALYSIS_ENGINE_URL}")
    print("=" * 60)
    
    # Run tests
    tests = [
        ("Analysis Engine", test_analysis_engine),
        ("n8n Webhook", test_n8n_webhook),
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # Update configuration
    update_extension_config()
    
    # Create workflow instructions
    create_n8n_workflow_instructions()
    
    # Run comprehensive test if basic tests pass
    if all(results.values()):
        print("\n🎯 Running comprehensive workflow test...")
        results["Full Workflow"] = create_test_request()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 SETUP SUMMARY")
    print("=" * 60)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<20} {status}")
    
    if all(results.values()):
        print("\n🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Import the n8n workflow (see n8n-setup-instructions.txt)")
        print("2. Install the Chrome extension")
        print("3. Visit a Kaggle notebook and test the assistant")
    else:
        print("\n⚠️  Some components need attention:")
        if not results.get("Analysis Engine"):
            print("- Start the analysis engine: cd analysis-engine && python main.py")
        if not results.get("n8n Webhook"):
            print("- Check n8n workflow setup (see instructions)")
    
    return 0 if all(results.values()) else 1

if __name__ == "__main__":
    sys.exit(main())
