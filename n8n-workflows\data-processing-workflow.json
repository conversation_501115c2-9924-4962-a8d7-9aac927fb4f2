{"name": "Data Processing Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "process-data", "responseMode": "responseNode", "options": {}}, "id": "data-webhook", "name": "Data Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "data-processing"}, {"parameters": {"functionCode": "// Process and validate data files\nconst payload = $json;\n\n// Extract file information\nconst fileData = payload.file_data;\nconst fileName = payload.file_name;\nconst fileType = payload.file_type;\n\nif (!fileData || !fileName) {\n  return [{\n    json: {\n      error: \"Missing file data or name\",\n      status: 400\n    }\n  }];\n}\n\n// Determine processing strategy based on file type\nlet processingStrategy = 'default';\nconst fileSize = fileData.length;\n\nif (fileSize > 10 * 1024 * 1024) { // 10MB\n  processingStrategy = 'large_file';\n} else if (fileType === 'excel') {\n  processingStrategy = 'excel';\n} else if (fileType === 'json') {\n  processingStrategy = 'json';\n}\n\nconst processedData = {\n  file_name: fileName,\n  file_type: fileType,\n  file_size: fileSize,\n  processing_strategy: processingStrategy,\n  data: fileData,\n  timestamp: new Date().toISOString(),\n  request_id: Date.now().toString()\n};\n\nconsole.log('Processing file:', {\n  name: fileName,\n  type: fileType,\n  size: fileSize,\n  strategy: processingStrategy\n});\n\nreturn [{ json: processedData }];"}, "id": "process-file-data", "name": "Process File Data", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.processing_strategy}}", "value2": "large_file"}]}}, "id": "check-file-size", "name": "Check File Size", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"functionCode": "// Handle large files with sampling\nconst data = $json;\n\n// Implement sampling strategy\nconst sampleSize = 10000; // Sample first 10k rows\nconst sampledData = {\n  ...data,\n  original_size: data.file_size,\n  sampled: true,\n  sample_size: sampleSize,\n  sampling_method: 'head',\n  note: `File too large (${Math.round(data.file_size / 1024 / 1024)}MB), using sample of ${sampleSize} rows`\n};\n\nconsole.log('Applied sampling to large file:', {\n  original_size: data.file_size,\n  sample_size: sampleSize\n});\n\nreturn [{ json: sampledData }];"}, "id": "handle-large-file", "name": "Handle Large File", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"functionCode": "// Process normal-sized files\nconst data = $json;\n\n// Add processing metadata\nconst processedData = {\n  ...data,\n  sampled: false,\n  processing_complete: true,\n  note: 'File processed without sampling'\n};\n\nconsole.log('Processed normal file:', {\n  name: data.file_name,\n  size: data.file_size\n});\n\nreturn [{ json: processedData }];"}, "id": "handle-normal-file", "name": "Handle Normal File", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"url": "={{$env.ANALYSIS_ENGINE_URL || 'http://localhost:8000'}}/analyze-file", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "multipart/form-data"}, {"name": "X-Request-ID", "value": "={{$json.request_id}}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "file", "value": "={{$json.data}}"}, {"name": "analysis_type", "value": "eda"}, {"name": "sampling_info", "value": "={{JSON.stringify({sampled: $json.sampled, sample_size: $json.sample_size})}}"}]}, "options": {"timeout": 60000}}, "id": "send-to-analysis", "name": "Send to Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [1120, 300]}, {"parameters": {"functionCode": "// Format final response\nconst analysisResult = $json;\nconst originalData = $node[\"Handle Large File\"].json || $node[\"Handle Normal File\"].json;\n\nconst response = {\n  success: true,\n  file_info: {\n    name: originalData.file_name,\n    type: originalData.file_type,\n    size: originalData.file_size,\n    sampled: originalData.sampled,\n    sample_size: originalData.sample_size\n  },\n  analysis: {\n    plan: analysisResult.plan,\n    code: analysisResult.code,\n    metadata: analysisResult.metadata\n  },\n  processing_info: {\n    strategy: originalData.processing_strategy,\n    timestamp: originalData.timestamp,\n    request_id: originalData.request_id\n  }\n};\n\nconsole.log('File processing completed:', {\n  request_id: originalData.request_id,\n  file_name: originalData.file_name,\n  success: true\n});\n\nreturn [{ json: response }];"}, "id": "format-response", "name": "Format Response", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"functionCode": "// Handle processing errors\nconst error = $json;\nconst originalData = $node[\"Process File Data\"].json;\n\nconst errorResponse = {\n  success: false,\n  error: error.message || 'File processing failed',\n  file_info: {\n    name: originalData?.file_name,\n    type: originalData?.file_type,\n    size: originalData?.file_size\n  },\n  timestamp: new Date().toISOString(),\n  request_id: originalData?.request_id\n};\n\nconsole.error('File processing error:', {\n  request_id: originalData?.request_id,\n  error: errorResponse.error\n});\n\nreturn [{ json: errorResponse }];"}, "id": "handle-error", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1340, 450]}, {"parameters": {"respondWith": "json", "responseBody": "={{$json}}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.webhookResponse", "typeVersion": 1, "position": [1560, 375]}], "connections": {"Data Webhook": {"main": [[{"node": "Process File Data", "type": "main", "index": 0}]]}, "Process File Data": {"main": [[{"node": "Check File Size", "type": "main", "index": 0}]]}, "Check File Size": {"main": [[{"node": "Handle Large File", "type": "main", "index": 0}], [{"node": "Handle Normal File", "type": "main", "index": 0}]]}, "Handle Large File": {"main": [[{"node": "Send to Analysis", "type": "main", "index": 0}]]}, "Handle Normal File": {"main": [[{"node": "Send to Analysis", "type": "main", "index": 0}]]}, "Send to Analysis": {"main": [[{"node": "Format Response", "type": "main", "index": 0}]], "error": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Format Response": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}, "Handle Error": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "UTC"}, "versionId": "1"}