"""
Main FastAPI application for the Kaggle Data Analysis Assistant
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import json
import logging
from pathlib import Path

from core.analyzer import DataAnalyzer
from core.plan_generator import PlanGenerator
from core.code_generator import CodeGenerator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Kaggle Data Analysis Assistant API",
    description="API for generating data analysis plans and Python code",
    version="1.0.0"
)

# Configure CORS for Chrome extension
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize core components
analyzer = DataAnalyzer()
plan_generator = PlanGenerator()
code_generator = CodeGenerator()


class AnalysisRequest(BaseModel):
    """Request model for analysis"""
    data_info: Dict[str, Any]  # Data metadata (columns, types, sample data)
    analysis_type: str  # 'eda', 'visualization', 'ml', 'stats'
    user_request: Optional[str] = None  # Specific user request
    data_source: str  # 'file', 'url', 'metadata_only'
    file_type: Optional[str] = None  # 'csv', 'excel', 'json'
    constraints: Optional[Dict[str, Any]] = None  # Size limits, sampling needs


class AnalysisResponse(BaseModel):
    """Response model for analysis"""
    plan: str
    code: str
    metadata: Optional[Dict[str, Any]] = None


@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Kaggle Data Analysis Assistant API is running"}


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "version": "1.0.0"}


@app.post("/analyze", response_model=AnalysisResponse)
async def analyze_data(request: AnalysisRequest):
    """
    Main endpoint for data analysis
    
    Args:
        request: Analysis request containing data info and requirements
        
    Returns:
        AnalysisResponse with plan and code
    """
    try:
        logger.info(f"Received analysis request: {request.analysis_type}")
        
        # Validate request
        if not request.data_info:
            raise HTTPException(status_code=400, detail="Data info is required")
        
        if request.analysis_type not in ['eda', 'visualization', 'ml', 'stats']:
            raise HTTPException(
                status_code=400, 
                detail="Analysis type must be one of: eda, visualization, ml, stats"
            )
        
        # Analyze data structure
        analysis_context = analyzer.analyze_data_structure(
            data_info=request.data_info,
            file_type=request.file_type,
            constraints=request.constraints
        )
        
        # Generate analysis plan
        plan = plan_generator.generate_plan(
            analysis_type=request.analysis_type,
            context=analysis_context,
            user_request=request.user_request
        )
        
        # Generate Python code
        code = code_generator.generate_code(
            analysis_type=request.analysis_type,
            context=analysis_context,
            plan=plan,
            user_request=request.user_request
        )
        
        # Prepare response
        response = AnalysisResponse(
            plan=plan,
            code=code,
            metadata={
                "analysis_type": request.analysis_type,
                "data_shape": analysis_context.get("data_shape"),
                "column_count": analysis_context.get("column_count"),
                "suggested_sampling": analysis_context.get("suggested_sampling")
            }
        )
        
        logger.info("Analysis completed successfully")
        return response
        
    except Exception as e:
        logger.error(f"Analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@app.post("/analyze-file")
async def analyze_file(
    file: UploadFile = File(...),
    analysis_type: str = "eda",
    user_request: Optional[str] = None
):
    """
    Analyze uploaded file directly
    
    Args:
        file: Uploaded data file
        analysis_type: Type of analysis to perform
        user_request: Specific user request
        
    Returns:
        AnalysisResponse with plan and code
    """
    try:
        # Save uploaded file temporarily
        file_path = f"/tmp/{file.filename}"
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Extract data info from file
        data_info = analyzer.extract_file_info(file_path, file.filename)
        
        # Create analysis request
        request = AnalysisRequest(
            data_info=data_info,
            analysis_type=analysis_type,
            user_request=user_request,
            data_source="file",
            file_type=Path(file.filename).suffix.lower().replace('.', '')
        )
        
        # Process analysis
        return await analyze_data(request)
        
    except Exception as e:
        logger.error(f"File analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"File analysis failed: {str(e)}")


@app.get("/templates")
async def get_templates():
    """Get available analysis templates"""
    return {
        "eda": "Exploratory Data Analysis - comprehensive data exploration",
        "visualization": "Data Visualization - charts and plots for insights",
        "ml": "Machine Learning - predictive modeling and classification",
        "stats": "Statistical Analysis - hypothesis testing and statistical inference"
    }


@app.get("/supported-formats")
async def get_supported_formats():
    """Get supported file formats"""
    return {
        "formats": ["csv", "excel", "json", "xlsx", "xls"],
        "max_size_mb": 100,
        "sampling_threshold_rows": 100000
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
