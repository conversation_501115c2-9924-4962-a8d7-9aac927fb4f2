# Installation Guide

This guide will help you set up the Kaggle Data Analysis Assistant on your system.

## Prerequisites

- **Python 3.8+** for the analysis engine
- **Node.js 16+** for n8n (if using n8n backend)
- **Chrome Browser** for the extension
- **Git** for cloning the repository

## Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/kaggle-analysis-assistant.git
cd kaggle-analysis-assistant
```

### 2. Set Up the Analysis Engine

```bash
cd analysis-engine

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Start the analysis engine
python main.py
```

The analysis engine will be available at `http://localhost:8000`

### 3. Install Chrome Extension

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right
3. Click "Load unpacked"
4. Select the `extension` folder from the project directory
5. The extension should now appear in your Chrome toolbar

### 4. Configure the Extension

1. Click the extension icon in Chrome
2. Go to Settings section
3. Set the Analysis Engine URL to `http://localhost:8000`
4. Save settings

## Advanced Setup with n8n

If you want to use n8n as a backend orchestrator:

### 1. Install n8n

```bash
npm install -g n8n
```

### 2. Start n8n

```bash
n8n start
```

n8n will be available at `http://localhost:5678`

### 3. Import Workflows

1. Open n8n web interface
2. Go to Workflows
3. Import the workflow files from `n8n-workflows/` directory:
   - `analysis-workflow.json`
   - `data-processing-workflow.json`

### 4. Configure Environment Variables

Set the following environment variables in n8n:

```bash
ANALYSIS_ENGINE_URL=http://localhost:8000
```

### 5. Update Extension Settings

1. Open extension settings
2. Enable "Use n8n Backend"
3. Set n8n Webhook URL to `http://localhost:5678/webhook/analysis`
4. Save settings

## Verification

### Test the Analysis Engine

```bash
curl -X GET http://localhost:8000/health
```

Expected response:
```json
{
  "status": "healthy",
  "version": "1.0.0"
}
```

### Test the Extension

1. Go to any Kaggle notebook
2. Click the extension icon
3. You should see "Connected" status
4. Try the "Detect Data" button

## Troubleshooting

### Common Issues

#### Analysis Engine Won't Start

- **Issue**: `ModuleNotFoundError` when starting
- **Solution**: Make sure virtual environment is activated and dependencies are installed

#### Extension Shows "Disconnected"

- **Issue**: Extension can't connect to analysis engine
- **Solution**: 
  1. Verify analysis engine is running on `http://localhost:8000`
  2. Check Chrome console for CORS errors
  3. Ensure firewall isn't blocking the connection

#### n8n Workflows Not Working

- **Issue**: Workflows fail with connection errors
- **Solution**:
  1. Verify `ANALYSIS_ENGINE_URL` environment variable is set
  2. Check that analysis engine is accessible from n8n
  3. Review n8n execution logs

### Port Conflicts

If default ports are in use:

#### Change Analysis Engine Port

Edit `analysis-engine/main.py`:
```python
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)  # Change port here
```

Update extension settings with new URL.

#### Change n8n Port

```bash
export N8N_PORT=5679
n8n start
```

### Performance Issues

#### Large Dataset Handling

The system automatically samples large datasets. To adjust:

Edit `analysis-engine/core/analyzer.py`:
```python
self.sampling_threshold = 50000  # Adjust threshold
```

#### Memory Usage

For high memory usage:

1. Reduce sampling size in analyzer
2. Use n8n backend to distribute load
3. Consider using a more powerful server

## Development Setup

For development and customization:

### Enable Debug Mode

Analysis Engine:
```python
# In main.py
uvicorn.run(app, host="0.0.0.0", port=8000, debug=True, reload=True)
```

Chrome Extension:
1. Enable Developer mode in Chrome
2. Check "Enable extension debugging"
3. Use Chrome DevTools for debugging

### Hot Reload

The analysis engine supports hot reload in development mode. Changes to Python files will automatically restart the server.

## Next Steps

- Read the [Usage Guide](usage.md) to learn how to use the assistant
- Check the [API Reference](api-reference.md) for detailed API documentation
- See [Development Guide](development.md) for customization options

## Support

If you encounter issues:

1. Check the [troubleshooting section](#troubleshooting) above
2. Review logs in:
   - Analysis engine console output
   - Chrome extension console (F12 → Console)
   - n8n execution logs (if using n8n)
3. Create an issue on GitHub with detailed error information
