#!/usr/bin/env python3
"""
Demo script showing the Kaggle Data Analysis Assistant capabilities
"""

import requests
import json
import time
from pathlib import Path

API_BASE_URL = "http://localhost:8000"

def demo_eda_analysis():
    """Demonstrate EDA analysis"""
    print("🔍 DEMO: Exploratory Data Analysis")
    print("-" * 40)
    
    # Simulate Titanic dataset
    titanic_data = {
        "data_info": {
            "columns": ["PassengerId", "Survived", "Pclass", "Name", "Sex", "Age", "SibSp", "Parch", "Ticket", "Fare", "Cabin", "Embarked"],
            "dtypes": {
                "PassengerId": "int64",
                "Survived": "int64",
                "Pclass": "int64", 
                "Name": "object",
                "Sex": "object",
                "Age": "float64",
                "SibSp": "int64",
                "Parch": "int64",
                "Ticket": "object",
                "Fare": "float64",
                "Cabin": "object",
                "Embarked": "object"
            },
            "shape": [891, 12],
            "sample_data": [
                {
                    "PassengerId": 1, "Survived": 0, "Pclass": 3, "Name": "<PERSON><PERSON>, <PERSON>. <PERSON>",
                    "Sex": "male", "Age": 22.0, "SibSp": 1, "Parch": 0, "Ticket": "A/5 21171",
                    "Fare": 7.25, "Cabin": None, "Embarked": "S"
                },
                {
                    "PassengerId": 2, "Survived": 1, "Pclass": 1, "Name": "Cumings, Mrs. John Bradley",
                    "Sex": "female", "Age": 38.0, "SibSp": 1, "Parch": 0, "Ticket": "PC 17599", 
                    "Fare": 71.2833, "Cabin": "C85", "Embarked": "C"
                }
            ]
        },
        "analysis_type": "eda",
        "user_request": "Analyze the Titanic dataset to understand passenger demographics and survival patterns",
        "data_source": "file",
        "file_type": "csv"
    }
    
    try:
        print("Sending EDA request...")
        response = requests.post(f"{API_BASE_URL}/analyze", json=titanic_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ EDA Analysis Generated!")
            print(f"\n📋 ANALYSIS PLAN:")
            print(result["plan"])
            
            print(f"\n💻 GENERATED CODE (first 500 chars):")
            print(result["code"][:500] + "..." if len(result["code"]) > 500 else result["code"])
            
            return True
        else:
            print(f"❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_ml_analysis():
    """Demonstrate ML analysis"""
    print("\n🤖 DEMO: Machine Learning Analysis")
    print("-" * 40)
    
    # Simulate house prices dataset
    housing_data = {
        "data_info": {
            "columns": ["id", "bedrooms", "bathrooms", "sqft_living", "sqft_lot", "floors", "waterfront", "view", "condition", "grade", "price"],
            "dtypes": {
                "id": "int64",
                "bedrooms": "int64",
                "bathrooms": "float64",
                "sqft_living": "int64",
                "sqft_lot": "int64", 
                "floors": "float64",
                "waterfront": "int64",
                "view": "int64",
                "condition": "int64",
                "grade": "int64",
                "price": "float64"
            },
            "shape": [21613, 11],
            "sample_data": [
                {
                    "id": 1, "bedrooms": 3, "bathrooms": 1.0, "sqft_living": 1180, "sqft_lot": 5650,
                    "floors": 1.0, "waterfront": 0, "view": 0, "condition": 3, "grade": 7, "price": 221900.0
                },
                {
                    "id": 2, "bedrooms": 3, "bathrooms": 2.25, "sqft_living": 2570, "sqft_lot": 7242,
                    "floors": 2.0, "waterfront": 0, "view": 0, "condition": 3, "grade": 7, "price": 538000.0
                }
            ]
        },
        "analysis_type": "ml",
        "user_request": "Build a model to predict house prices based on property features",
        "data_source": "file",
        "file_type": "csv"
    }
    
    try:
        print("Sending ML request...")
        response = requests.post(f"{API_BASE_URL}/analyze", json=housing_data, timeout=45)
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ ML Analysis Generated!")
            print(f"\n📋 ANALYSIS PLAN:")
            print(result["plan"])
            
            print(f"\n💻 GENERATED CODE (first 500 chars):")
            print(result["code"][:500] + "..." if len(result["code"]) > 500 else result["code"])
            
            return True
        else:
            print(f"❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_visualization():
    """Demonstrate visualization analysis"""
    print("\n📊 DEMO: Data Visualization")
    print("-" * 40)
    
    # Simulate sales dataset
    sales_data = {
        "data_info": {
            "columns": ["date", "product_category", "sales_amount", "quantity", "region", "customer_segment"],
            "dtypes": {
                "date": "datetime64",
                "product_category": "object",
                "sales_amount": "float64",
                "quantity": "int64",
                "region": "object",
                "customer_segment": "object"
            },
            "shape": [10000, 6],
            "sample_data": [
                {
                    "date": "2023-01-01", "product_category": "Electronics", "sales_amount": 1250.50,
                    "quantity": 5, "region": "North", "customer_segment": "Corporate"
                },
                {
                    "date": "2023-01-02", "product_category": "Clothing", "sales_amount": 890.25,
                    "quantity": 12, "region": "South", "customer_segment": "Consumer"
                }
            ]
        },
        "analysis_type": "visualization",
        "user_request": "Create visualizations to show sales trends by region and product category",
        "data_source": "file",
        "file_type": "csv"
    }
    
    try:
        print("Sending visualization request...")
        response = requests.post(f"{API_BASE_URL}/analyze", json=sales_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Visualization Analysis Generated!")
            print(f"\n📋 ANALYSIS PLAN:")
            print(result["plan"])
            
            print(f"\n💻 GENERATED CODE (first 500 chars):")
            print(result["code"][:500] + "..." if len(result["code"]) > 500 else result["code"])
            
            return True
        else:
            print(f"❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_stats_analysis():
    """Demonstrate statistical analysis"""
    print("\n📈 DEMO: Statistical Analysis")
    print("-" * 40)
    
    # Simulate A/B test dataset
    ab_test_data = {
        "data_info": {
            "columns": ["user_id", "test_group", "conversion", "time_on_site", "page_views", "age", "gender"],
            "dtypes": {
                "user_id": "int64",
                "test_group": "object",
                "conversion": "int64",
                "time_on_site": "float64",
                "page_views": "int64",
                "age": "int64",
                "gender": "object"
            },
            "shape": [5000, 7],
            "sample_data": [
                {
                    "user_id": 1, "test_group": "A", "conversion": 1, "time_on_site": 245.5,
                    "page_views": 8, "age": 28, "gender": "F"
                },
                {
                    "user_id": 2, "test_group": "B", "conversion": 0, "time_on_site": 123.2,
                    "page_views": 3, "age": 35, "gender": "M"
                }
            ]
        },
        "analysis_type": "stats",
        "user_request": "Analyze A/B test results to determine if there's a significant difference in conversion rates",
        "data_source": "file",
        "file_type": "csv"
    }
    
    try:
        print("Sending statistical analysis request...")
        response = requests.post(f"{API_BASE_URL}/analyze", json=ab_test_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Statistical Analysis Generated!")
            print(f"\n📋 ANALYSIS PLAN:")
            print(result["plan"])
            
            print(f"\n💻 GENERATED CODE (first 500 chars):")
            print(result["code"][:500] + "..." if len(result["code"]) > 500 else result["code"])
            
            return True
        else:
            print(f"❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_service_health():
    """Check if the analysis service is running"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Analysis service is running")
            return True
        else:
            print(f"❌ Service health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException:
        print(f"❌ Cannot connect to analysis service at {API_BASE_URL}")
        print("   Make sure the analysis engine is running:")
        print("   cd analysis-engine && python main.py")
        return False

def main():
    """Run the demo"""
    print("🎯 Kaggle Data Analysis Assistant - DEMO")
    print("=" * 50)
    
    # Check service health
    if not check_service_health():
        return 1
    
    print("\nThis demo will show the assistant's capabilities across different analysis types.\n")
    
    demos = [
        ("EDA Analysis", demo_eda_analysis),
        ("Machine Learning", demo_ml_analysis), 
        ("Data Visualization", demo_visualization),
        ("Statistical Analysis", demo_stats_analysis)
    ]
    
    results = []
    
    for demo_name, demo_func in demos:
        print(f"\n{'='*60}")
        success = demo_func()
        results.append((demo_name, success))
        
        if success:
            print(f"\n✅ {demo_name} demo completed successfully!")
        else:
            print(f"\n❌ {demo_name} demo failed!")
        
        # Small delay between demos
        time.sleep(1)
    
    # Summary
    print(f"\n{'='*60}")
    print("📋 DEMO SUMMARY")
    print("=" * 60)
    
    successful = sum(1 for _, success in results if success)
    total = len(results)
    
    for demo_name, success in results:
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{demo_name:<20} {status}")
    
    print(f"\nOverall: {successful}/{total} demos successful")
    
    if successful == total:
        print("\n🎉 All demos completed successfully!")
        print("\nThe Kaggle Data Analysis Assistant is ready to help with your data science projects!")
        print("\nNext steps:")
        print("1. Install the Chrome extension")
        print("2. Visit a Kaggle notebook")
        print("3. Click the 'Analyze Data' button")
        print("4. Select your data and analysis type")
        print("5. Get instant analysis plans and code!")
    else:
        print(f"\n⚠️  {total - successful} demo(s) failed. Check the analysis engine setup.")
    
    return 0 if successful == total else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
