#!/usr/bin/env python3
"""
System integration test script for Kaggle Data Analysis Assistant
"""

import requests
import json
import time
import sys
from pathlib import Path

# Configuration
API_BASE_URL = "http://localhost:8000"
N8N_WEBHOOK_URL = "http://localhost:5678/webhook/analysis"

def test_analysis_engine():
    """Test the analysis engine directly"""
    print("🔍 Testing Analysis Engine...")
    
    try:
        # Test health endpoint
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Analysis engine is healthy")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to analysis engine: {e}")
        return False
    
    # Test analysis endpoint
    test_data = {
        "data_info": {
            "columns": ["PassengerId", "Survived", "Pclass", "Name", "Sex", "Age"],
            "dtypes": {
                "PassengerId": "int64",
                "Survived": "int64", 
                "Pclass": "int64",
                "Name": "object",
                "Sex": "object",
                "Age": "float64"
            },
            "shape": [891, 6],
            "sample_data": [
                {"PassengerId": 1, "Survived": 0, "Pclass": 3, "Name": "Braund, Mr. Owen Harris", "Sex": "male", "Age": 22.0},
                {"PassengerId": 2, "Survived": 1, "Pclass": 1, "Name": "Cumings, Mrs. John Bradley", "Sex": "female", "Age": 38.0}
            ]
        },
        "analysis_type": "eda",
        "data_source": "metadata_only",
        "file_type": "csv"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/analyze", json=test_data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            if "plan" in result and "code" in result:
                print("✅ Analysis generation successful")
                print(f"   Plan length: {len(result['plan'])} characters")
                print(f"   Code length: {len(result['code'])} characters")
                return True
            else:
                print("❌ Invalid response format")
                return False
        else:
            print(f"❌ Analysis failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Analysis request failed: {e}")
        return False

def test_n8n_workflow():
    """Test the n8n workflow"""
    print("\n🔄 Testing n8n Workflow...")
    
    test_payload = {
        "source": "test_script",
        "timestamp": time.time(),
        "payload": {
            "data_info": {
                "columns": ["feature1", "feature2", "target"],
                "dtypes": {
                    "feature1": "float64",
                    "feature2": "float64",
                    "target": "object"
                },
                "shape": [1000, 3]
            },
            "analysis_type": "visualization",
            "data_source": "metadata_only",
            "file_type": "csv"
        }
    }
    
    try:
        response = requests.post(N8N_WEBHOOK_URL, json=test_payload, timeout=45)
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ n8n workflow successful")
                return True
            else:
                print(f"❌ n8n workflow failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ n8n webhook failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to n8n: {e}")
        print("   (This is expected if n8n is not running)")
        return False

def test_all_analysis_types():
    """Test all analysis types"""
    print("\n📊 Testing All Analysis Types...")
    
    base_data = {
        "data_info": {
            "columns": ["id", "feature1", "feature2", "category", "target"],
            "dtypes": {
                "id": "int64",
                "feature1": "float64",
                "feature2": "float64", 
                "category": "object",
                "target": "int64"
            },
            "shape": [5000, 5],
            "sample_data": [
                {"id": 1, "feature1": 1.5, "feature2": 2.3, "category": "A", "target": 1},
                {"id": 2, "feature1": 2.1, "feature2": 1.8, "category": "B", "target": 0}
            ]
        },
        "data_source": "metadata_only",
        "file_type": "csv"
    }
    
    analysis_types = ["eda", "visualization", "ml", "stats"]
    results = {}
    
    for analysis_type in analysis_types:
        print(f"   Testing {analysis_type.upper()}...")
        
        test_data = base_data.copy()
        test_data["analysis_type"] = analysis_type
        
        try:
            response = requests.post(f"{API_BASE_URL}/analyze", json=test_data, timeout=30)
            if response.status_code == 200:
                result = response.json()
                results[analysis_type] = {
                    "success": True,
                    "plan_length": len(result.get("plan", "")),
                    "code_length": len(result.get("code", ""))
                }
                print(f"   ✅ {analysis_type.upper()} successful")
            else:
                results[analysis_type] = {"success": False, "error": response.text}
                print(f"   ❌ {analysis_type.upper()} failed: {response.status_code}")
        except requests.exceptions.RequestException as e:
            results[analysis_type] = {"success": False, "error": str(e)}
            print(f"   ❌ {analysis_type.upper()} failed: {e}")
    
    return results

def test_file_upload():
    """Test file upload functionality"""
    print("\n📁 Testing File Upload...")
    
    # Create a simple test CSV
    test_csv_content = """id,name,value,category
1,Alice,10.5,A
2,Bob,20.3,B
3,Charlie,15.7,A
4,Diana,25.1,C
5,Eve,30.9,B"""
    
    try:
        files = {"file": ("test_data.csv", test_csv_content, "text/csv")}
        data = {"analysis_type": "eda"}
        
        response = requests.post(f"{API_BASE_URL}/analyze-file", files=files, data=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            if "plan" in result and "code" in result:
                print("✅ File upload and analysis successful")
                return True
            else:
                print("❌ Invalid file analysis response")
                return False
        else:
            print(f"❌ File upload failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ File upload request failed: {e}")
        return False

def test_large_dataset():
    """Test large dataset handling"""
    print("\n📈 Testing Large Dataset Handling...")
    
    large_dataset_info = {
        "data_info": {
            "columns": [f"feature_{i}" for i in range(50)],  # 50 columns
            "dtypes": {f"feature_{i}": "float64" for i in range(50)},
            "shape": [150000, 50],  # Large dataset
            "sample_data": [
                {f"feature_{i}": float(i * 1.5) for i in range(50)}
            ]
        },
        "analysis_type": "eda",
        "data_source": "metadata_only",
        "file_type": "csv"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/analyze", json=large_dataset_info, timeout=45)
        if response.status_code == 200:
            result = response.json()
            metadata = result.get("metadata", {})
            suggested_sampling = metadata.get("suggested_sampling")
            
            if suggested_sampling and suggested_sampling.get("needed"):
                print("✅ Large dataset handling successful (sampling suggested)")
                return True
            else:
                print("❌ Large dataset not handled properly (no sampling suggested)")
                return False
        else:
            print(f"❌ Large dataset test failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Large dataset test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Kaggle Data Analysis Assistant System Tests\n")
    
    tests = [
        ("Analysis Engine", test_analysis_engine),
        ("n8n Workflow", test_n8n_workflow),
        ("File Upload", test_file_upload),
        ("Large Dataset", test_large_dataset)
    ]
    
    results = {}
    
    # Run basic tests
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # Run analysis types test
    print("\n📊 Testing All Analysis Types...")
    analysis_results = test_all_analysis_types()
    results["Analysis Types"] = all(r.get("success", False) for r in analysis_results.values())
    
    # Print summary
    print("\n" + "="*50)
    print("📋 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<20} {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to use.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
