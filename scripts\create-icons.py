#!/usr/bin/env python3
"""
Create simple icon files for the Chrome extension
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, output_path):
    """Create a simple icon with the specified size"""
    
    # Create a new image with a blue background
    img = Image.new('RGBA', (size, size), (0, 123, 255, 255))
    draw = ImageDraw.Draw(img)
    
    # Draw a simple chart/graph icon
    # Background circle
    center = size // 2
    radius = size // 3
    draw.ellipse([center - radius, center - radius, center + radius, center + radius], 
                 fill=(255, 255, 255, 255), outline=(0, 123, 255, 255), width=2)
    
    # Draw a simple bar chart inside
    bar_width = size // 8
    bar_spacing = size // 12
    start_x = center - (bar_width * 1.5 + bar_spacing)
    start_y = center + radius // 2
    
    # Three bars of different heights
    heights = [radius // 3, radius // 2, radius // 4]
    colors = [(52, 152, 219, 255), (46, 204, 113, 255), (241, 196, 15, 255)]
    
    for i, (height, color) in enumerate(zip(heights, colors)):
        x = start_x + i * (bar_width + bar_spacing)
        y = start_y - height
        draw.rectangle([x, y, x + bar_width, start_y], fill=color)
    
    # Add a small "K" for Kaggle
    try:
        # Try to use a font, fallback to default if not available
        font_size = max(8, size // 6)
        font = ImageFont.load_default()
        text = "K"
        
        # Get text size
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # Position text in top-right
        text_x = size - text_width - 2
        text_y = 2
        
        draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)
    except:
        # If font loading fails, just skip the text
        pass
    
    # Save the image
    img.save(output_path, 'PNG')
    print(f"Created icon: {output_path}")

def main():
    """Create all required icon sizes"""
    
    # Get the extension directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(script_dir)
    icons_dir = os.path.join(project_dir, 'extension', 'icons')
    
    # Create icons directory if it doesn't exist
    os.makedirs(icons_dir, exist_ok=True)
    
    # Create icons in different sizes
    sizes = [16, 48, 128]
    
    for size in sizes:
        icon_path = os.path.join(icons_dir, f'icon{size}.png')
        create_icon(size, icon_path)
    
    print(f"\n✅ All icons created in: {icons_dir}")
    print("Icons created:")
    for size in sizes:
        print(f"  - icon{size}.png ({size}x{size})")

if __name__ == "__main__":
    try:
        main()
    except ImportError:
        print("❌ PIL (Pillow) is required to create icons.")
        print("Install it with: pip install Pillow")
        print("\nAlternatively, you can create simple PNG icons manually:")
        print("- extension/icons/icon16.png (16x16)")
        print("- extension/icons/icon48.png (48x48)")  
        print("- extension/icons/icon128.png (128x128)")
    except Exception as e:
        print(f"❌ Error creating icons: {e}")
        print("\nYou can create simple PNG icons manually:")
        print("- extension/icons/icon16.png (16x16)")
        print("- extension/icons/icon48.png (48x48)")
        print("- extension/icons/icon128.png (128x128)")
