openapi: 3.0.3
info:
  title: Kaggle Data Analysis Assistant API
  description: API for generating data analysis plans and Python code for Kaggle notebooks
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8000
    description: Local development server
  - url: https://api.kaggle-assistant.com
    description: Production server

paths:
  /:
    get:
      summary: Health check
      description: Basic health check endpoint
      responses:
        '200':
          description: Service is running
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Kaggle Data Analysis Assistant API is running"

  /health:
    get:
      summary: Detailed health check
      description: Detailed health status of the service
      responses:
        '200':
          description: Service health status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  version:
                    type: string
                    example: "1.0.0"

  /analyze:
    post:
      summary: Analyze data
      description: Generate analysis plan and Python code based on data information
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AnalysisRequest'
      responses:
        '200':
          description: Analysis completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisResponse'
        '400':
          description: Bad request - invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /analyze-file:
    post:
      summary: Analyze uploaded file
      description: Analyze data from an uploaded file
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: Data file to analyze
                analysis_type:
                  type: string
                  enum: [eda, visualization, ml, stats]
                  default: eda
                user_request:
                  type: string
                  description: Specific user request or question
      responses:
        '200':
          description: File analysis completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /templates:
    get:
      summary: Get analysis templates
      description: Get available analysis templates and their descriptions
      responses:
        '200':
          description: Available templates
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: string
                example:
                  eda: "Exploratory Data Analysis - comprehensive data exploration"
                  visualization: "Data Visualization - charts and plots for insights"
                  ml: "Machine Learning - predictive modeling and classification"
                  stats: "Statistical Analysis - hypothesis testing and statistical inference"

  /supported-formats:
    get:
      summary: Get supported file formats
      description: Get information about supported file formats and limitations
      responses:
        '200':
          description: Supported formats information
          content:
            application/json:
              schema:
                type: object
                properties:
                  formats:
                    type: array
                    items:
                      type: string
                    example: ["csv", "excel", "json", "xlsx", "xls"]
                  max_size_mb:
                    type: integer
                    example: 100
                  sampling_threshold_rows:
                    type: integer
                    example: 100000

components:
  schemas:
    AnalysisRequest:
      type: object
      required:
        - data_info
        - analysis_type
        - data_source
      properties:
        data_info:
          type: object
          description: Data metadata including columns, types, and sample data
          properties:
            columns:
              type: array
              items:
                type: string
              description: Column names
            dtypes:
              type: object
              additionalProperties:
                type: string
              description: Data types for each column
            shape:
              type: array
              items:
                type: integer
              description: Dataset shape [rows, columns]
            sample_data:
              type: array
              items:
                type: object
              description: Sample rows from the dataset
        analysis_type:
          type: string
          enum: [eda, visualization, ml, stats]
          description: Type of analysis to perform
        user_request:
          type: string
          nullable: true
          description: Specific user request or question
        data_source:
          type: string
          enum: [file, url, metadata_only]
          description: Source of the data
        file_type:
          type: string
          nullable: true
          enum: [csv, excel, json, xlsx, xls]
          description: Type of data file
        constraints:
          type: object
          nullable: true
          description: Any constraints or limitations

    AnalysisResponse:
      type: object
      properties:
        plan:
          type: string
          description: Step-by-step analysis plan
        code:
          type: string
          description: Ready-to-run Python code
        metadata:
          type: object
          nullable: true
          properties:
            analysis_type:
              type: string
            data_shape:
              type: array
              items:
                type: integer
            column_count:
              type: integer
            suggested_sampling:
              type: object

    ErrorResponse:
      type: object
      properties:
        detail:
          type: string
          description: Error message
        status_code:
          type: integer
          description: HTTP status code

  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

security:
  - ApiKeyAuth: []
