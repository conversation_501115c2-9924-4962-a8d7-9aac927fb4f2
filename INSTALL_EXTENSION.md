# Chrome Extension Installation Guide

## 🚀 Quick Installation Steps

### 1. Open Chrome Extensions Page
- Open Google Chrome
- Go to `chrome://extensions/` 
- Or click the three dots menu → More tools → Extensions

### 2. Enable Developer Mode
- In the top-right corner, toggle **"Developer mode"** to ON
- This will show additional options

### 3. Load the Extension
- Click **"Load unpacked"** button (appears after enabling developer mode)
- Navigate to your project folder: `c:\Users\<USER>\OneDrive\Desktop\Desktop\my\Projects\kaggle agent 2`
- Select the **`extension`** folder (not the root project folder)
- Click **"Select Folder"**

### 4. Verify Installation
- You should see "Kaggle Data Analysis Assistant" in your extensions list
- The extension should show as "Enabled"
- You should see the extension icon in your Chrome toolbar

### 5. Pin the Extension (Optional)
- Click the puzzle piece icon in Chrome toolbar
- Find "Kaggle Data Analysis Assistant"
- Click the pin icon to keep it visible

## 🔧 Troubleshooting

### "Manifest file is missing or unreadable"
This error usually means:

1. **Wrong folder selected**: Make sure you select the `extension` folder, not the root project folder
2. **File permissions**: Make sure Chrome has permission to read the files
3. **Path issues**: Avoid spaces or special characters in the path

### "Extensions must be enabled"
- Make sure Developer mode is turned ON
- Try refreshing the extensions page

### "Could not load extension"
- Check that all required files exist:
  - `extension/manifest.json` ✅
  - `extension/background.js` ✅
  - `extension/content.js` ✅
  - `extension/popup/popup.html` ✅
  - `extension/icons/icon16.png` ✅
  - `extension/icons/icon48.png` ✅
  - `extension/icons/icon128.png` ✅

### Extension loads but doesn't work
1. **Check permissions**: The extension needs permission for kaggle.com
2. **Start analysis engine**: Make sure the analysis engine is running
   ```bash
   cd analysis-engine
   python main.py
   ```
3. **Test connectivity**: Run the test script
   ```bash
   python scripts/setup-n8n.py
   ```

## 📁 Correct Folder Structure

Make sure your extension folder has this structure:

```
extension/
├── manifest.json
├── background.js
├── content.js
├── styles.css
├── icons/
│   ├── icon16.png
│   ├── icon48.png
│   └── icon128.png
├── popup/
│   ├── popup.html
│   ├── popup.css
│   └── popup.js
└── utils/
    └── kaggle-detector.js
```

## ✅ Verification Steps

After installation:

1. **Check extension is loaded**:
   - Go to `chrome://extensions/`
   - Find "Kaggle Data Analysis Assistant"
   - Should show "Enabled" status

2. **Test on Kaggle**:
   - Go to any Kaggle notebook
   - You should see a floating "Analyze Data" button
   - Or click the extension icon in the toolbar

3. **Check popup**:
   - Click the extension icon
   - Should open a popup with analysis options

## 🎯 Next Steps

Once the extension is installed:

1. **Start the analysis engine**:
   ```bash
   cd analysis-engine
   python main.py
   ```

2. **Test the system**:
   ```bash
   python scripts/setup-n8n.py
   ```

3. **Visit a Kaggle notebook** and try the analysis features!

## 📞 Still Having Issues?

If you're still getting the manifest error:

1. **Try a different browser**: Test with a fresh Chrome profile
2. **Check file permissions**: Make sure all files are readable
3. **Restart Chrome**: Close and reopen Chrome completely
4. **Check the exact error**: Look for more details in the Chrome developer console

The extension should now load successfully! 🎉
