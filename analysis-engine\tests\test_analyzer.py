"""
Tests for the DataAnalyzer class
"""

import pytest
import pandas as pd
import numpy as np
from core.analyzer import DataAnalyzer


class TestDataAnalyzer:
    
    def setup_method(self):
        """Setup test fixtures"""
        self.analyzer = DataAnalyzer()
        
        # Sample data info for testing
        self.sample_data_info = {
            "columns": ["id", "name", "age", "salary", "department"],
            "dtypes": {
                "id": "int64",
                "name": "object", 
                "age": "int64",
                "salary": "float64",
                "department": "object"
            },
            "shape": [1000, 5],
            "sample_data": [
                {"id": 1, "name": "<PERSON>", "age": 25, "salary": 50000.0, "department": "Engineering"},
                {"id": 2, "name": "<PERSON>", "age": 30, "salary": 60000.0, "department": "Marketing"},
                {"id": 3, "name": "<PERSON>", "age": 35, "salary": 70000.0, "department": "Engineering"}
            ]
        }
    
    def test_analyze_data_structure_basic(self):
        """Test basic data structure analysis"""
        context = self.analyzer.analyze_data_structure(
            data_info=self.sample_data_info,
            file_type="csv"
        )
        
        assert context["file_type"] == "csv"
        assert context["columns"] == self.sample_data_info["columns"]
        assert context["column_count"] == 5
        assert context["data_shape"] == [1000, 5]
        assert context["row_count"] == 1000
    
    def test_categorize_data_types(self):
        """Test data type categorization"""
        dtypes = {
            "id": "int64",
            "name": "object",
            "age": "int64", 
            "salary": "float64",
            "is_active": "bool",
            "created_date": "datetime64"
        }
        
        categories = self.analyzer._categorize_data_types(dtypes)
        
        assert "id" in categories["numeric"]
        assert "age" in categories["numeric"]
        assert "salary" in categories["numeric"]
        assert "name" in categories["categorical"]
        assert "is_active" in categories["boolean"]
        assert "created_date" in categories["datetime"]
    
    def test_sampling_suggestion_large_dataset(self):
        """Test sampling suggestion for large datasets"""
        large_data_info = self.sample_data_info.copy()
        large_data_info["shape"] = [150000, 5]
        
        context = self.analyzer.analyze_data_structure(
            data_info=large_data_info,
            file_type="csv"
        )
        
        assert context["suggested_sampling"]["needed"] is True
        assert context["suggested_sampling"]["sample_size"] <= 75000
        assert "reason" in context["suggested_sampling"]
    
    def test_sampling_suggestion_small_dataset(self):
        """Test no sampling for small datasets"""
        context = self.analyzer.analyze_data_structure(
            data_info=self.sample_data_info,
            file_type="csv"
        )
        
        assert context["suggested_sampling"]["needed"] is False
    
    def test_analyze_sample_data(self):
        """Test sample data analysis"""
        sample_data = [
            {"category": "A", "value": 10, "flag": True},
            {"category": "B", "value": 20, "flag": False},
            {"category": "A", "value": 15, "flag": True},
            {"category": "C", "value": 25, "flag": False}
        ]
        
        analysis = self.analyzer._analyze_sample_data(sample_data)
        
        assert analysis["sample_size"] == 4
        assert "category" in analysis["unique_counts"]
        assert analysis["unique_counts"]["category"] == 3
        assert "category" in analysis["potential_targets"]
        assert "value" in analysis["value_ranges"]
        assert analysis["value_ranges"]["value"]["min"] == 10
        assert analysis["value_ranges"]["value"]["max"] == 25
    
    def test_suggest_analyses(self):
        """Test analysis suggestions"""
        context = {
            "data_types": {
                "numeric": ["age", "salary"],
                "categorical": ["department", "level"],
                "datetime": [],
                "text": [],
                "boolean": []
            },
            "potential_targets": ["level"]
        }
        
        suggestions = self.analyzer._suggest_analyses(context)
        
        assert "eda" in suggestions
        assert "visualization" in suggestions
        assert "ml" in suggestions
        assert "stats" in suggestions
    
    def test_extract_csv_info(self, tmp_path):
        """Test CSV file information extraction"""
        # Create a temporary CSV file
        csv_file = tmp_path / "test.csv"
        df = pd.DataFrame({
            "id": [1, 2, 3, 4, 5],
            "name": ["A", "B", "C", "D", "E"],
            "value": [10.5, 20.3, 15.7, 25.1, 30.9]
        })
        df.to_csv(csv_file, index=False)
        
        info = self.analyzer._extract_csv_info(str(csv_file))
        
        assert info["file_type"] == "csv"
        assert len(info["columns"]) == 3
        assert "id" in info["columns"]
        assert "name" in info["columns"]
        assert "value" in info["columns"]
        assert info["shape"][0] == 5  # 5 rows
        assert info["shape"][1] == 3  # 3 columns
    
    def test_extract_file_info_unsupported_format(self):
        """Test handling of unsupported file formats"""
        with pytest.raises(ValueError, match="Unsupported file format"):
            self.analyzer.extract_file_info("/path/to/file.txt", "file.txt")
    
    def test_analyze_data_structure_with_constraints(self):
        """Test data structure analysis with constraints"""
        constraints = {
            "max_memory_mb": 500,
            "max_processing_time": 300
        }
        
        context = self.analyzer.analyze_data_structure(
            data_info=self.sample_data_info,
            file_type="csv",
            constraints=constraints
        )
        
        assert context["constraints"] == constraints
    
    def test_analyze_data_structure_missing_info(self):
        """Test analysis with minimal data info"""
        minimal_info = {
            "columns": ["col1", "col2"]
        }
        
        context = self.analyzer.analyze_data_structure(
            data_info=minimal_info,
            file_type="csv"
        )
        
        assert context["columns"] == ["col1", "col2"]
        assert context["column_count"] == 2
        # Should handle missing fields gracefully
        assert "suggested_sampling" in context


if __name__ == "__main__":
    pytest.main([__file__])
