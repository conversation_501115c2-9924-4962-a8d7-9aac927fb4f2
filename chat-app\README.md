# Kaggle Data Analysis Assistant - Chat App

A conversational interface for data analysis assistance. Chat with an AI assistant to get instant data analysis, visualizations, machine learning models, and statistical insights.

## 🚀 Features

### 💬 **Chat Interface**
- Natural language conversation with AI assistant
- Upload datasets and get instant analysis
- Ask questions about your data in plain English
- Get step-by-step analysis plans and Python code

### 📊 **Analysis Types**
- **Exploratory Data Analysis (EDA)**: Data overview, statistics, missing values
- **Data Visualization**: Charts, plots, dashboards
- **Machine Learning**: Predictive models, classification, regression
- **Statistical Analysis**: Hypothesis testing, correlations, significance tests

### 📁 **File Support**
- CSV files
- Excel files (.xlsx, .xls)
- JSON files
- Automatic data type detection
- Data preview and summary

### ⚡ **Quick Actions**
- One-click analysis buttons
- Smart analysis type detection from your messages
- Instant code generation
- Real-time responses

## 🛠️ Installation

### 1. Install Dependencies
```bash
cd chat-app
pip install -r requirements.txt
```

### 2. Start the Analysis Engine
```bash
cd ../analysis-engine
python main.py
```

### 3. Run the Chat App
```bash
cd ../chat-app
streamlit run main.py
```

The chat app will open in your browser at `http://localhost:8501`

## 💬 How to Use

### **Basic Chat**
1. **Start chatting**: Type your questions in the chat input
2. **Upload data**: Use the sidebar to upload CSV, Excel, or JSON files
3. **Get analysis**: Ask for specific analysis types or let the AI detect what you need

### **Example Conversations**

**🔍 Exploratory Data Analysis**
```
You: "I uploaded a sales dataset. Can you analyze it?"
Assistant: [Provides EDA plan and Python code]
```

**📊 Visualizations**
```
You: "Create some charts to show sales trends by region"
Assistant: [Generates visualization code with matplotlib/seaborn]
```

**🤖 Machine Learning**
```
You: "Help me predict customer churn using this data"
Assistant: [Creates ML pipeline with model training and evaluation]
```

**📈 Statistics**
```
You: "Is there a significant difference between group A and B?"
Assistant: [Performs statistical tests and provides interpretation]
```

### **Quick Actions**
Use the sidebar buttons for instant analysis:
- 🔍 **EDA**: Complete exploratory data analysis
- 📊 **Visualizations**: Create charts and plots
- 🤖 **ML Model**: Build predictive models
- 📈 **Statistics**: Perform statistical tests

## 🔧 Configuration

### **Backend Options**
The chat app can connect to:
1. **n8n Webhook**: `https://sabryroby.app.n8n.cloud/webhook-test/kaggle-analysis`
2. **Direct Analysis Engine**: `http://localhost:8000`

### **Settings**
- Toggle n8n backend in the sidebar
- Clear chat history
- Upload different file formats

## 📊 What You Get

### **Analysis Plans**
Detailed step-by-step plans for your analysis:
- Data loading and preprocessing
- Analysis steps with time estimates
- Library recommendations
- Best practices

### **Python Code**
Production-ready code with:
- Proper imports and setup
- Error handling
- Comments and documentation
- Visualization code
- Model evaluation metrics

### **Smart Responses**
The AI assistant:
- Understands your data context
- Suggests appropriate analysis methods
- Explains statistical concepts
- Provides actionable insights

## 🎯 Example Use Cases

### **Data Scientist**
- Quick EDA on new datasets
- Prototype ML models
- Generate visualization code
- Statistical hypothesis testing

### **Business Analyst**
- Analyze sales data
- Create business dashboards
- Compare performance metrics
- Generate insights reports

### **Student/Researcher**
- Learn data analysis techniques
- Get code examples
- Understand statistical methods
- Practice with real datasets

## 🔍 Troubleshooting

### **Chat app won't start**
```bash
pip install streamlit pandas requests openpyxl
streamlit run main.py
```

### **Analysis engine not responding**
Make sure the analysis engine is running:
```bash
cd analysis-engine
python main.py
```

### **File upload issues**
- Check file format (CSV, Excel, JSON)
- Ensure file is not corrupted
- Try smaller files first

### **No responses from assistant**
- Check internet connection
- Verify analysis engine is running
- Try toggling n8n backend setting

## 🚀 Advanced Features

### **Custom Analysis**
Ask specific questions like:
- "What are the top 5 features for predicting sales?"
- "Show me the correlation between age and income"
- "Create a time series plot of monthly revenue"

### **Multi-step Analysis**
Have conversations that build on previous analysis:
- Upload data → Get EDA → Ask for specific visualizations → Build ML model

### **Code Explanation**
Ask the assistant to explain generated code:
- "Explain this machine learning pipeline"
- "What does this statistical test mean?"
- "How do I interpret these results?"

---

**Start chatting with your AI data analysis assistant! 🤖📊**
