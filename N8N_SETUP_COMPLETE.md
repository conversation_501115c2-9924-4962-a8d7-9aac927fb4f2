# Complete n8n Setup Guide

## ✅ **Current Status**

- ✅ **Analysis Engine**: Running at `http://localhost:8000`
- ✅ **Chrome Extension**: Fixed and ready to install
- ✅ **n8n Webhook URL**: `https://sabryroby.app.n8n.cloud/webhook-test/kaggle-analysis`
- ⏳ **n8n Workflow**: Needs to be imported (next step)

## 🚀 **Step-by-Step Setup**

### **Step 1: Import Workflow to n8n Cloud**

1. **Go to your n8n instance**: https://sabryroby.app.n8n.cloud/
2. **Login** to your account
3. **Click "Workflows"** in the left sidebar
4. **Click "Import from file"** button
5. **Upload the file**: `n8n-workflows/analysis-workflow.json`
6. **Click "Import"**

### **Step 2: Configure the Workflow**

1. **Open the imported workflow** (click on it)
2. **Check the webhook node**:
   - Should be set to path: `kaggle-analysis`
   - Method: `POST`
   - Full URL should be: `https://sabryroby.app.n8n.cloud/webhook-test/kaggle-analysis`

3. **Set Environment Variables**:
   - Go to **Settings** → **Environment Variables**
   - Add: `ANALYSIS_ENGINE_URL = http://localhost:8000`
   
   > **Note**: If your analysis engine is on a different machine or you want to deploy it, use that URL instead.

### **Step 3: Activate the Workflow**

1. **In the workflow editor**, find the **"Active"** toggle (top-right)
2. **Turn it ON** (should turn green)
3. **Save the workflow** (Ctrl+S or click Save)

### **Step 4: Test the Workflow**

Run this test from your computer:

```bash
python scripts/test-webhook.py
```

Expected result: ✅ Both tests should pass

## 🔧 **Important Notes**

### **Network Connectivity**

Since your analysis engine is running locally (`localhost:8000`), n8n cloud cannot reach it directly. You have two options:

#### **Option A: Use ngrok (Recommended for testing)**
1. **Install ngrok**: https://ngrok.com/
2. **Expose your local server**:
   ```bash
   ngrok http 8000
   ```
3. **Update environment variable** in n8n to the ngrok URL (e.g., `https://abc123.ngrok.io`)

#### **Option B: Deploy Analysis Engine to Cloud**
Deploy to Heroku, Railway, or any cloud service and update the environment variable.

#### **Option C: Direct Mode (Fallback)**
If n8n doesn't work, the extension can connect directly to your local analysis engine by disabling n8n in the extension settings.

## 🧪 **Testing the Complete System**

### **1. Test Analysis Engine**
```bash
curl http://localhost:8000/health
```
Should return: `{"status":"healthy","version":"1.0.0"}`

### **2. Test n8n Webhook**
```bash
python scripts/test-webhook.py
```

### **3. Test Chrome Extension**
1. **Load the extension** in Chrome (`chrome://extensions/`)
2. **Go to a Kaggle notebook**
3. **Click the extension icon**
4. **Try the analysis features**

### **4. Full System Test**
```bash
python scripts/setup-n8n.py
```

## 📊 **Expected Data Flow**

```
Chrome Extension → n8n Cloud Webhook → Analysis Engine → n8n Cloud → Chrome Extension
```

## 🔍 **Troubleshooting**

### **Webhook returns 404**
- ✅ Import the workflow
- ✅ Activate the workflow
- ✅ Check webhook path is `kaggle-analysis`

### **Workflow fails with connection error**
- ✅ Check `ANALYSIS_ENGINE_URL` environment variable
- ✅ Use ngrok if analysis engine is local
- ✅ Ensure analysis engine is running

### **Extension doesn't work**
- ✅ Reload the extension in Chrome
- ✅ Check browser console for errors
- ✅ Follow the [MANUAL_TEST_GUIDE.md](MANUAL_TEST_GUIDE.md)

## 🎯 **Quick Verification Checklist**

- [ ] n8n workflow imported and active
- [ ] Environment variable `ANALYSIS_ENGINE_URL` set
- [ ] Analysis engine running (`curl http://localhost:8000/health`)
- [ ] Webhook test passes (`python scripts/test-webhook.py`)
- [ ] Chrome extension loaded without errors
- [ ] Extension popup opens and works

## 📞 **Next Steps**

Once everything is set up:

1. **Visit a Kaggle notebook** with data
2. **Click the extension icon** or "Analyze Data" button
3. **Select your data source**
4. **Choose analysis type** (EDA, ML, Visualization, Stats)
5. **Get instant analysis plans and code!**

---

**Your Kaggle Data Analysis Assistant is almost ready! 🎉**

Just import the workflow to n8n and you'll be all set!
