#!/usr/bin/env python3
"""
Launcher script for the Kaggle Data Analysis Assistant Chat App
"""

import subprocess
import sys
import time
import webbrowser
import os
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = ['streamlit', 'pandas', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("Installing missing packages...")
        
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', 
                '-r', 'chat-app/requirements.txt'
            ])
            print("✅ Dependencies installed successfully")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            return False
    
    return True

def check_analysis_engine():
    """Check if analysis engine is running"""
    try:
        import requests
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Analysis engine is running")
            return True
        else:
            print("⚠️  Analysis engine responded with error")
            return False
    except:
        print("❌ Analysis engine is not running")
        return False

def start_analysis_engine():
    """Start the analysis engine"""
    print("🚀 Starting analysis engine...")
    
    engine_dir = Path("analysis-engine")
    if not engine_dir.exists():
        print("❌ Analysis engine directory not found")
        return None
    
    try:
        # Start analysis engine in background
        process = subprocess.Popen([
            sys.executable, "main.py"
        ], cwd=engine_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for it to start
        time.sleep(3)
        
        # Check if it's running
        if check_analysis_engine():
            print("✅ Analysis engine started successfully")
            return process
        else:
            print("❌ Failed to start analysis engine")
            return None
            
    except Exception as e:
        print(f"❌ Error starting analysis engine: {e}")
        return None

def start_chat_app():
    """Start the Streamlit chat app"""
    print("🚀 Starting chat app...")
    
    chat_dir = Path("chat-app")
    if not chat_dir.exists():
        print("❌ Chat app directory not found")
        return False
    
    try:
        # Start Streamlit app
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "main.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ], cwd=chat_dir)
        
        return True
        
    except KeyboardInterrupt:
        print("\n👋 Chat app stopped by user")
        return True
    except Exception as e:
        print(f"❌ Error starting chat app: {e}")
        return False

def main():
    """Main launcher function"""
    print("🤖 Kaggle Data Analysis Assistant - Chat App Launcher")
    print("=" * 60)
    
    # Check dependencies
    print("📦 Checking dependencies...")
    if not check_dependencies():
        print("❌ Failed to install dependencies. Please install manually:")
        print("   pip install streamlit pandas requests openpyxl")
        return 1
    
    # Check if analysis engine is running
    print("\n🔍 Checking analysis engine...")
    engine_process = None
    
    if not check_analysis_engine():
        print("🚀 Starting analysis engine...")
        engine_process = start_analysis_engine()
        
        if not engine_process:
            print("❌ Could not start analysis engine")
            print("\nManual start instructions:")
            print("1. Open a new terminal")
            print("2. cd analysis-engine")
            print("3. python main.py")
            print("4. Then run this script again")
            return 1
    
    # Start chat app
    print("\n💬 Starting chat app...")
    print("🌐 The chat app will open in your browser at: http://localhost:8501")
    print("\n💡 Usage tips:")
    print("   - Upload a CSV/Excel file using the sidebar")
    print("   - Ask questions like 'Analyze my data' or 'Create visualizations'")
    print("   - Use quick action buttons for instant analysis")
    print("   - Press Ctrl+C to stop")
    
    try:
        # Small delay then open browser
        time.sleep(2)
        webbrowser.open("http://localhost:8501")
        
        # Start the chat app (this will block until stopped)
        success = start_chat_app()
        
        return 0 if success else 1
        
    finally:
        # Clean up analysis engine if we started it
        if engine_process:
            print("\n🛑 Stopping analysis engine...")
            engine_process.terminate()
            engine_process.wait()

if __name__ == "__main__":
    sys.exit(main())
