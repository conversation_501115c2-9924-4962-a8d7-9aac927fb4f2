/**
 * Popup script for Kaggle Data Analysis Assistant
 */

// DOM elements
let elements = {};

// Initialize popup
document.addEventListener('DOMContentLoaded', async () => {
    initializeElements();
    await loadSettings();
    checkConnectionStatus();
    checkKaggleStatus();
    loadAnalysisHistory();
    setupEventListeners();
});

/**
 * Initialize DOM element references
 */
function initializeElements() {
    elements = {
        connectionStatus: document.getElementById('connection-status'),
        statusText: document.getElementById('status-text'),
        kaggleStatusText: document.getElementById('kaggle-status-text'),
        detectDataBtn: document.getElementById('detect-data-btn'),
        quickEdaBtn: document.getElementById('quick-eda-btn'),
        visualizeBtn: document.getElementById('visualize-btn'),
        analysisHistory: document.getElementById('analysis-history'),
        apiUrl: document.getElementById('api-url'),
        useN8n: document.getElementById('use-n8n'),
        n8nUrl: document.getElementById('n8n-url'),
        n8nUrlSetting: document.getElementById('n8n-url-setting'),
        autoDetect: document.getElementById('auto-detect'),
        saveSettingsBtn: document.getElementById('save-settings-btn'),
        loadingOverlay: document.getElementById('loading-overlay'),
        notification: document.getElementById('notification'),
        notificationText: document.getElementById('notification-text'),
        notificationClose: document.getElementById('notification-close')
    };
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Quick action buttons
    elements.detectDataBtn.addEventListener('click', detectData);
    elements.quickEdaBtn.addEventListener('click', () => quickAnalysis('eda'));
    elements.visualizeBtn.addEventListener('click', () => quickAnalysis('visualization'));
    
    // Settings
    elements.useN8n.addEventListener('change', toggleN8nSettings);
    elements.saveSettingsBtn.addEventListener('click', saveSettings);
    
    // Notification close
    elements.notificationClose.addEventListener('click', hideNotification);
    
    // Help links
    document.getElementById('help-link').addEventListener('click', () => {
        chrome.tabs.create({ url: 'https://github.com/your-repo/kaggle-assistant/wiki' });
    });
    
    document.getElementById('feedback-link').addEventListener('click', () => {
        chrome.tabs.create({ url: 'https://github.com/your-repo/kaggle-assistant/issues' });
    });
    
    document.getElementById('about-link').addEventListener('click', showAbout);
}

/**
 * Load settings from storage
 */
async function loadSettings() {
    try {
        const response = await sendMessage({ action: 'getConfig' });
        if (response.success) {
            const config = response.config;
            elements.apiUrl.value = config.apiUrl || '';
            elements.useN8n.checked = config.useN8n || false;
            elements.n8nUrl.value = config.n8nUrl || '';
            elements.autoDetect.checked = config.autoDetect !== false;
            
            toggleN8nSettings();
        }
    } catch (error) {
        console.error('Failed to load settings:', error);
    }
}

/**
 * Save settings to storage
 */
async function saveSettings() {
    showLoading(true);
    
    try {
        const config = {
            apiUrl: elements.apiUrl.value,
            useN8n: elements.useN8n.checked,
            n8nUrl: elements.n8nUrl.value,
            autoDetect: elements.autoDetect.checked
        };
        
        const response = await sendMessage({ action: 'updateConfig', config });
        if (response.success) {
            showNotification('Settings saved successfully', 'success');
            // Recheck connection with new settings
            setTimeout(checkConnectionStatus, 500);
        } else {
            throw new Error(response.error);
        }
    } catch (error) {
        showNotification(`Failed to save settings: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Toggle n8n URL setting visibility
 */
function toggleN8nSettings() {
    elements.n8nUrlSetting.style.display = elements.useN8n.checked ? 'block' : 'none';
}

/**
 * Check connection status to analysis engine
 */
async function checkConnectionStatus() {
    try {
        const config = await getConfig();
        const apiUrl = config.useN8n ? config.n8nUrl : config.apiUrl;
        
        // Try to ping the service
        const response = await fetch(`${config.apiUrl}/health`, {
            method: 'GET',
            signal: AbortSignal.timeout(5000)
        });
        
        if (response.ok) {
            elements.connectionStatus.className = 'status-dot online';
            elements.statusText.textContent = 'Connected';
        } else {
            throw new Error('Service unavailable');
        }
    } catch (error) {
        elements.connectionStatus.className = 'status-dot offline';
        elements.statusText.textContent = 'Disconnected';
    }
}

/**
 * Check if current tab is Kaggle
 */
async function checkKaggleStatus() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        const isKaggle = tab.url && tab.url.includes('kaggle.com');
        
        if (isKaggle) {
            elements.kaggleStatusText.textContent = 'On Kaggle';
            elements.kaggleStatusText.style.color = '#27ae60';
            
            // Enable action buttons
            elements.detectDataBtn.disabled = false;
            elements.quickEdaBtn.disabled = false;
            elements.visualizeBtn.disabled = false;
        } else {
            elements.kaggleStatusText.textContent = 'Not on Kaggle';
            elements.kaggleStatusText.style.color = '#e74c3c';
            
            // Disable action buttons
            elements.detectDataBtn.disabled = true;
            elements.quickEdaBtn.disabled = true;
            elements.visualizeBtn.disabled = true;
        }
    } catch (error) {
        console.error('Failed to check Kaggle status:', error);
    }
}

/**
 * Detect data in current Kaggle notebook
 */
async function detectData() {
    showLoading(true);
    
    try {
        const response = await sendMessage({ action: 'detectKaggleData' });
        if (response.success) {
            showNotification('Data detection completed', 'success');
            // The content script will handle the UI update
        } else {
            throw new Error(response.error);
        }
    } catch (error) {
        showNotification(`Data detection failed: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Perform quick analysis
 */
async function quickAnalysis(analysisType) {
    showLoading(true);
    
    try {
        // First detect data
        const dataResponse = await sendMessage({ action: 'detectKaggleData' });
        if (!dataResponse.success) {
            throw new Error('Failed to detect data');
        }
        
        // Prepare analysis request with detected data
        const analysisData = {
            dataInfo: dataResponse.data.dataInfo || {},
            analysisType: analysisType,
            userRequest: null,
            dataSource: 'auto_detected'
        };
        
        const response = await sendMessage({ action: 'analyzeData', data: analysisData });
        if (response.success) {
            showNotification(`${analysisType.toUpperCase()} analysis completed`, 'success');
            addToHistory(analysisType, new Date());
        } else {
            throw new Error(response.error);
        }
    } catch (error) {
        showNotification(`Analysis failed: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Load analysis history
 */
async function loadAnalysisHistory() {
    try {
        const result = await chrome.storage.local.get(['analysisHistory']);
        const history = result.analysisHistory || [];
        
        if (history.length === 0) {
            elements.analysisHistory.innerHTML = '<p class="no-history">No recent analysis</p>';
            return;
        }
        
        const historyHtml = history.slice(0, 5).map(item => `
            <div class="history-item">
                <div class="history-type">${item.type.toUpperCase()}</div>
                <div class="history-date">${new Date(item.date).toLocaleDateString()}</div>
            </div>
        `).join('');
        
        elements.analysisHistory.innerHTML = historyHtml;
    } catch (error) {
        console.error('Failed to load history:', error);
    }
}

/**
 * Add item to analysis history
 */
async function addToHistory(analysisType, date) {
    try {
        const result = await chrome.storage.local.get(['analysisHistory']);
        const history = result.analysisHistory || [];
        
        history.unshift({
            type: analysisType,
            date: date.toISOString()
        });
        
        // Keep only last 10 items
        if (history.length > 10) {
            history.splice(10);
        }
        
        await chrome.storage.local.set({ analysisHistory: history });
        loadAnalysisHistory();
    } catch (error) {
        console.error('Failed to save to history:', error);
    }
}

/**
 * Show/hide loading overlay
 */
function showLoading(show) {
    elements.loadingOverlay.style.display = show ? 'flex' : 'none';
}

/**
 * Show notification
 */
function showNotification(message, type = 'success') {
    elements.notificationText.textContent = message;
    elements.notification.className = `notification ${type}`;
    elements.notification.style.display = 'flex';
    
    // Auto-hide after 3 seconds
    setTimeout(hideNotification, 3000);
}

/**
 * Hide notification
 */
function hideNotification() {
    elements.notification.style.display = 'none';
}

/**
 * Show about dialog
 */
function showAbout() {
    alert(`Kaggle Data Analysis Assistant v1.0.0

An AI-powered extension that helps you analyze data in Kaggle Notebooks.

Features:
- Automatic data detection
- Intelligent analysis planning
- Ready-to-run Python code generation
- Multiple analysis types (EDA, ML, Stats, Visualization)

Developed with ❤️ for the data science community.`);
}

/**
 * Get current configuration
 */
async function getConfig() {
    const response = await sendMessage({ action: 'getConfig' });
    return response.success ? response.config : {};
}

/**
 * Send message to background script
 */
function sendMessage(message) {
    return new Promise((resolve) => {
        chrome.runtime.sendMessage(message, resolve);
    });
}
