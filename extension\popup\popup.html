<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kaggle Data Analysis Assistant</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="popup-container">
        <header class="popup-header">
            <div class="logo">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <h1>Data Analysis Assistant</h1>
            </div>
            <div class="version">v1.0.0</div>
        </header>

        <main class="popup-content">
            <!-- Status Section -->
            <section class="status-section">
                <div class="status-indicator">
                    <div id="connection-status" class="status-dot offline"></div>
                    <span id="status-text">Checking connection...</span>
                </div>
                <div id="kaggle-detection" class="kaggle-status">
                    <span id="kaggle-status-text">Not on Kaggle</span>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="quick-actions">
                <h3>Quick Actions</h3>
                <div class="action-buttons">
                    <button id="detect-data-btn" class="action-btn" disabled>
                        <span class="btn-icon">🔍</span>
                        Detect Data
                    </button>
                    <button id="quick-eda-btn" class="action-btn" disabled>
                        <span class="btn-icon">📊</span>
                        Quick EDA
                    </button>
                    <button id="visualize-btn" class="action-btn" disabled>
                        <span class="btn-icon">📈</span>
                        Visualize
                    </button>
                </div>
            </section>

            <!-- Analysis History -->
            <section class="history-section">
                <h3>Recent Analysis</h3>
                <div id="analysis-history" class="history-list">
                    <p class="no-history">No recent analysis</p>
                </div>
            </section>

            <!-- Settings -->
            <section class="settings-section">
                <h3>Settings</h3>
                <div class="setting-item">
                    <label for="api-url">Analysis Engine URL:</label>
                    <input type="text" id="api-url" placeholder="http://localhost:8000">
                </div>
                <div class="setting-item">
                    <label for="use-n8n">Use n8n Backend:</label>
                    <input type="checkbox" id="use-n8n" checked>
                </div>
                <div class="setting-item" id="n8n-url-setting">
                    <label for="n8n-url">n8n Webhook URL:</label>
                    <input type="text" id="n8n-url" placeholder="https://sabryroby.app.n8n.cloud/webhook-test/kaggle-analysis">
                </div>
                <div class="setting-item">
                    <label for="auto-detect">Auto-detect Data:</label>
                    <input type="checkbox" id="auto-detect" checked>
                </div>
                <button id="save-settings-btn" class="save-btn">Save Settings</button>
            </section>

            <!-- Help & Info -->
            <section class="help-section">
                <h3>Help & Info</h3>
                <div class="help-links">
                    <a href="#" id="help-link">User Guide</a>
                    <a href="#" id="feedback-link">Send Feedback</a>
                    <a href="#" id="about-link">About</a>
                </div>
            </section>
        </main>

        <!-- Loading Overlay -->
        <div id="loading-overlay" class="loading-overlay" style="display: none;">
            <div class="spinner"></div>
            <p>Processing...</p>
        </div>

        <!-- Notification -->
        <div id="notification" class="notification" style="display: none;">
            <span id="notification-text"></span>
            <button id="notification-close">×</button>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
