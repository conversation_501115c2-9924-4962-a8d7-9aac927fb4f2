# Kaggle Data Analysis Assistant

A comprehensive AI-powered Chrome extension with n8n backend that provides intelligent data analysis assistance for Kaggle Notebooks, similar to the Augment extension. This system automatically detects data, generates analysis plans, and produces ready-to-run Python code for various data science tasks.

## 🚀 Quick Start with n8n Cloud

### Prerequisites
- Python 3.8+
- Chrome Browser
- Access to n8n cloud: `https://sabryroby.app.n8n.cloud/`

### 1. Start the Analysis Engine
```bash
cd analysis-engine
pip install -r requirements.txt
python main.py
```

### 2. Setup n8n Integration
```bash
python scripts/setup-n8n.py
```

### 3. Import Workflow to n8n
1. Go to https://sabryroby.app.n8n.cloud/
2. Import `n8n-workflows/analysis-workflow.json`
3. Set environment variable: `ANALYSIS_ENGINE_URL=http://localhost:8000`
4. Activate the workflow

### 4. Install Chrome Extension
1. Open `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked" and select the `extension` folder

### 5. Test the System
```bash
python scripts/test-system.py
```

📖 **See [QUICK_START_N8N.md](QUICK_START_N8N.md) for detailed setup instructions**

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│  Chrome         │    │   n8n        │    │  Analysis       │
│  Extension      │◄──►│   Backend    │◄──►│  Engine         │
│  (Frontend)     │    │  (Workflow)  │    │  (Python)       │
└─────────────────┘    └──────────────┘    └─────────────────┘
```

## ✨ Features

### 🔍 **Automatic Data Detection**
- Detects uploaded CSV, Excel, JSON files
- Identifies pandas DataFrames in code cells
- Recognizes Kaggle dataset files
- Extracts data structure and metadata

### 🧠 **Intelligent Analysis Planning**
- Creates step-by-step analysis plans
- Adapts to data characteristics
- Considers dataset size and complexity
- Provides time estimates

### 💻 **Code Generation**
- Generates clean, commented Python code
- Uses industry-standard libraries
- Handles large datasets with sampling
- Modular and customizable output

### 📊 **Multiple Analysis Types**

#### 1. **Exploratory Data Analysis (EDA)**
- Dataset overview and statistics
- Missing value analysis
- Data type analysis
- Correlation analysis
- Basic visualizations

#### 2. **Data Visualization**
- Distribution plots
- Correlation heatmaps
- Interactive charts with Plotly
- Multi-panel dashboards

#### 3. **Machine Learning**
- Data preprocessing
- Feature engineering
- Model training and evaluation
- Feature importance analysis

#### 4. **Statistical Analysis**
- Hypothesis testing
- Correlation significance
- Group comparisons (t-tests, ANOVA)
- Confidence intervals

## 📁 Project Structure

```
kaggle-analysis-assistant/
├── extension/                  # Chrome Extension
│   ├── manifest.json
│   ├── background.js
│   ├── content.js
│   ├── popup/
│   └── utils/
├── analysis-engine/           # Python Analysis Engine
│   ├── main.py
│   ├── core/
│   ├── processors/
│   ├── templates/
│   └── tests/
├── n8n-workflows/            # n8n Backend Workflows
├── docs/                     # Documentation
├── examples/                 # Sample data and notebooks
└── scripts/                  # Build and test scripts
```

## 🛠️ Installation & Setup

See the detailed [Installation Guide](docs/installation.md) for complete setup instructions.

### Quick Setup
1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/kaggle-analysis-assistant.git
   cd kaggle-analysis-assistant
   ```

2. **Set up Analysis Engine**
   ```bash
   cd analysis-engine
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   python main.py
   ```

3. **Install Chrome Extension**
   - Open Chrome → Extensions → Developer mode
   - Load unpacked → Select `extension` folder

4. **Test the system**
   ```bash
   python scripts/test-system.py
   ```

## 📖 Usage

See the [Usage Guide](docs/usage.md) for detailed instructions.

### Basic Workflow
1. **Open a Kaggle Notebook**
2. **Click the "Analyze Data" button** (appears in top-right)
3. **Select your data source** from detected files/DataFrames
4. **Choose analysis type** (EDA, Visualization, ML, Stats)
5. **Add specific requests** (optional)
6. **Generate analysis** and get instant results

### Example Output

**Analysis Plan:**
```
Exploratory Data Analysis Plan
==============================

Dataset Overview:
- Shape: (891, 12)
- Columns: 12
- File Type: csv

Analysis Steps:
1. Load the complete dataset
2. Display basic dataset information (shape, columns, data types)
3. Check for missing values and data quality issues
4. Generate descriptive statistics for numerical columns
5. Analyze categorical variables (value counts, unique values)
6. Calculate correlation matrix for numerical variables
7. Create distribution plots for key variables
8. Provide insights and recommendations for further analysis

Estimated Time: 16-24 minutes
Libraries Used: pandas, matplotlib, seaborn
```

**Generated Code:**
```python
"""
EDA Analysis - Generated by Kaggle Data Analysis Assistant
Dataset: csv file with 12 columns
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Load the complete dataset
df = pd.read_csv('your_data.csv')

# Basic dataset information
print("Dataset Shape:", df.shape)
print("\nColumn Names:")
print(df.columns.tolist())
# ... more code
```

## 🧪 Testing

### Run Unit Tests
```bash
cd analysis-engine
pytest tests/
```

### Run System Tests
```bash
python scripts/test-system.py
```

### Run Demo
```bash
python scripts/demo.py
```

## 🔧 Advanced Configuration

### n8n Backend Setup (Optional)
1. Install n8n: `npm install -g n8n`
2. Start n8n: `n8n start`
3. Import workflows from `n8n-workflows/`
4. Set environment variable: `ANALYSIS_ENGINE_URL=http://localhost:8000`
5. Update extension settings to use n8n webhook

### Custom Analysis Templates
Add custom analysis templates in `analysis-engine/templates/`:
```python
def custom_analysis_template(context, user_request):
    # Your custom analysis logic
    return analysis_steps
```

## 📊 API Reference

The analysis engine provides a REST API. See [API Reference](docs/api-reference.md) for details.

### Key Endpoints
- `GET /health` - Health check
- `POST /analyze` - Generate analysis from metadata
- `POST /analyze-file` - Analyze uploaded file
- `GET /templates` - Available analysis templates

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- 📖 [Documentation](docs/)
- 🐛 [Issue Tracker](https://github.com/your-username/kaggle-analysis-assistant/issues)
- 💬 [Discussions](https://github.com/your-username/kaggle-analysis-assistant/discussions)

## 🙏 Acknowledgments

- Inspired by the Augment extension
- Built for the data science community
- Uses open-source libraries: pandas, scikit-learn, matplotlib, seaborn

---

**Made with ❤️ for data scientists and analysts working on Kaggle**
