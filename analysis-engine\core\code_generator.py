"""
Python code generator for data analysis
"""

from typing import Dict, Any, Optional, List
import logging
from jinja2 import Template

logger = logging.getLogger(__name__)


class CodeGenerator:
    """Generates ready-to-run Python code for data analysis"""
    
    def __init__(self):
        self.code_templates = {
            "eda": self._generate_eda_code,
            "visualization": self._generate_visualization_code,
            "ml": self._generate_ml_code,
            "stats": self._generate_stats_code
        }
    
    def generate_code(
        self, 
        analysis_type: str, 
        context: Dict[str, Any], 
        plan: str,
        user_request: Optional[str] = None
    ) -> str:
        """
        Generate Python code based on analysis type and context
        
        Args:
            analysis_type: Type of analysis
            context: Analysis context
            plan: Generated analysis plan
            user_request: Specific user request
            
        Returns:
            Ready-to-run Python code as string
        """
        try:
            if analysis_type not in self.code_templates:
                raise ValueError(f"Unsupported analysis type: {analysis_type}")
            
            # Generate code
            code_generator = self.code_templates[analysis_type]
            code = code_generator(context, user_request)
            
            # Add header and imports
            full_code = self._add_header_and_imports(analysis_type, context) + "\n\n" + code
            
            return full_code
            
        except Exception as e:
            logger.error(f"Error generating code: {str(e)}")
            raise
    
    def _add_header_and_imports(self, analysis_type: str, context: Dict[str, Any]) -> str:
        """Add header comment and necessary imports"""
        
        header = f'''"""
{analysis_type.upper()} Analysis - Generated by Kaggle Data Analysis Assistant
Dataset: {context.get("file_type", "Unknown")} file with {context.get("column_count", "Unknown")} columns
Generated: {context.get("analysis_timestamp", "Unknown")}
"""

# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)'''

        # Add specific imports based on analysis type
        if analysis_type == "ml":
            header += '''
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.metrics import classification_report, confusion_matrix, mean_squared_error, r2_score
import xgboost as xgb'''

        elif analysis_type == "stats":
            header += '''
from scipy import stats
from scipy.stats import chi2_contingency, ttest_ind, f_oneway
import statsmodels.api as sm
from statsmodels.stats.multicomp import pairwise_tukeyhsd'''

        elif analysis_type == "visualization":
            header += '''
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots'''

        return header
    
    def _generate_eda_code(self, context: Dict[str, Any], user_request: Optional[str]) -> str:
        """Generate EDA code"""
        
        # Determine data loading approach
        file_type = context.get("file_type", "csv")
        sampling = context.get("suggested_sampling", {})
        
        code_parts = []
        
        # Data loading
        if file_type == "csv":
            if sampling.get("needed"):
                code_parts.append(f'''# Load data with sampling (large dataset detected)
df = pd.read_csv('your_data.csv', nrows={sampling.get("sample_size", 50000)})
print(f"Loaded sample of {{len(df)}} rows for analysis")''')
            else:
                code_parts.append('''# Load the complete dataset
df = pd.read_csv('your_data.csv')''')
        elif file_type in ["excel", "xlsx"]:
            code_parts.append('''# Load Excel data
df = pd.read_excel('your_data.xlsx')''')
        else:
            code_parts.append('''# Load data (adjust path and method as needed)
df = pd.read_csv('your_data.csv')  # Change to appropriate loading method''')
        
        # Basic information
        code_parts.append('''
# Basic dataset information
print("Dataset Shape:", df.shape)
print("\\nColumn Names:")
print(df.columns.tolist())
print("\\nData Types:")
print(df.dtypes)
print("\\nFirst 5 rows:")
print(df.head())''')
        
        # Missing values analysis
        code_parts.append('''
# Missing values analysis
print("\\nMissing Values:")
missing_data = df.isnull().sum()
missing_percent = 100 * missing_data / len(df)
missing_table = pd.DataFrame({
    'Missing Count': missing_data,
    'Percentage': missing_percent
})
print(missing_table[missing_table['Missing Count'] > 0].sort_values('Missing Count', ascending=False))''')
        
        # Descriptive statistics
        code_parts.append('''
# Descriptive statistics for numerical columns
print("\\nDescriptive Statistics:")
print(df.describe())''')
        
        # Data type specific analysis
        data_types = context.get("data_types", {})
        
        if data_types.get("categorical"):
            code_parts.append(f'''
# Categorical variables analysis
categorical_cols = {data_types["categorical"]}
print("\\nCategorical Variables Analysis:")
for col in categorical_cols:
    if col in df.columns:
        print(f"\\n{{col}} - Unique values: {{df[col].nunique()}}")
        print(df[col].value_counts().head(10))''')
        
        if data_types.get("numeric"):
            code_parts.append(f'''
# Numerical variables analysis
numerical_cols = {data_types["numeric"]}
print("\\nNumerical Variables Analysis:")
for col in numerical_cols:
    if col in df.columns:
        print(f"\\n{{col}}:")
        print(f"  Mean: {{df[col].mean():.2f}}")
        print(f"  Median: {{df[col].median():.2f}}")
        print(f"  Std: {{df[col].std():.2f}}")
        print(f"  Min: {{df[col].min():.2f}}")
        print(f"  Max: {{df[col].max():.2f}}")''')
        
        # Correlation analysis
        if len(data_types.get("numeric", [])) > 1:
            code_parts.append('''
# Correlation analysis
print("\\nCorrelation Matrix:")
correlation_matrix = df.select_dtypes(include=[np.number]).corr()
print(correlation_matrix)

# Correlation heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
plt.title('Correlation Heatmap')
plt.tight_layout()
plt.show()''')
        
        # Basic visualizations
        code_parts.append('''
# Basic visualizations
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# Distribution of first numerical column
numerical_columns = df.select_dtypes(include=[np.number]).columns
if len(numerical_columns) > 0:
    df[numerical_columns[0]].hist(bins=30, ax=axes[0,0])
    axes[0,0].set_title(f'Distribution of {numerical_columns[0]}')
    
    # Box plot for outlier detection
    df.boxplot(column=numerical_columns[0], ax=axes[0,1])
    axes[0,1].set_title(f'Box Plot - {numerical_columns[0]}')

# Categorical distribution (if available)
categorical_columns = df.select_dtypes(include=['object']).columns
if len(categorical_columns) > 0:
    df[categorical_columns[0]].value_counts().head(10).plot(kind='bar', ax=axes[1,0])
    axes[1,0].set_title(f'Distribution of {categorical_columns[0]}')
    axes[1,0].tick_params(axis='x', rotation=45)

# Missing values heatmap
sns.heatmap(df.isnull(), cbar=True, ax=axes[1,1])
axes[1,1].set_title('Missing Values Pattern')

plt.tight_layout()
plt.show()''')
        
        # User-specific analysis
        if user_request:
            code_parts.append(f'''
# User-specific analysis: {user_request}
# TODO: Add specific analysis based on user request
print("\\nUser Request Analysis:")
print("Please customize this section based on your specific question: {user_request}")''')
        
        # Summary insights
        code_parts.append('''
# Summary insights
print("\\n" + "="*50)
print("SUMMARY INSIGHTS")
print("="*50)
print(f"1. Dataset contains {len(df)} rows and {len(df.columns)} columns")
print(f"2. Missing values found in {len(missing_table[missing_table['Missing Count'] > 0])} columns")
print(f"3. Numerical columns: {len(df.select_dtypes(include=[np.number]).columns)}")
print(f"4. Categorical columns: {len(df.select_dtypes(include=['object']).columns)}")
print("\\nRecommendations for next steps:")
print("- Handle missing values appropriately")
print("- Consider feature engineering for categorical variables")
print("- Investigate outliers in numerical columns")
print("- Explore relationships between variables further")''')
        
        return "\n".join(code_parts)

    def _generate_visualization_code(self, context: Dict[str, Any], user_request: Optional[str]) -> str:
        """Generate visualization code"""

        file_type = context.get("file_type", "csv")
        data_types = context.get("data_types", {})

        code_parts = []

        # Data loading
        if file_type == "csv":
            code_parts.append('''# Load data for visualization
df = pd.read_csv('your_data.csv')''')
        else:
            code_parts.append('''# Load data (adjust as needed)
df = pd.read_csv('your_data.csv')''')

        code_parts.append('''
# Setup for visualizations
plt.style.use('seaborn-v0_8')
fig_count = 1''')

        # Numerical distributions
        if data_types.get("numeric"):
            code_parts.append(f'''
# Numerical variable distributions
numerical_cols = {data_types["numeric"]}
n_numeric = len([col for col in numerical_cols if col in df.columns])

if n_numeric > 0:
    fig, axes = plt.subplots((n_numeric + 1) // 2, 2, figsize=(15, 4 * ((n_numeric + 1) // 2)))
    if n_numeric == 1:
        axes = [axes]
    elif n_numeric == 2:
        axes = axes.reshape(1, -1)

    for i, col in enumerate([c for c in numerical_cols if c in df.columns]):
        row, col_idx = i // 2, i % 2
        ax = axes[row, col_idx] if n_numeric > 2 else axes[col_idx]

        # Histogram with KDE
        df[col].hist(bins=30, alpha=0.7, ax=ax)
        df[col].plot.kde(ax=ax, secondary_y=True, color='red')
        ax.set_title(f'Distribution of {{col}}')
        ax.set_xlabel(col)
        ax.set_ylabel('Frequency')

    plt.tight_layout()
    plt.show()''')

        # Categorical distributions
        if data_types.get("categorical"):
            code_parts.append(f'''
# Categorical variable distributions
categorical_cols = {data_types["categorical"]}
available_cats = [col for col in categorical_cols if col in df.columns]

for col in available_cats[:3]:  # Limit to first 3 categorical columns
    plt.figure(figsize=(12, 6))

    # Bar plot
    plt.subplot(1, 2, 1)
    value_counts = df[col].value_counts().head(10)
    value_counts.plot(kind='bar')
    plt.title(f'Top 10 Values in {{col}}')
    plt.xticks(rotation=45)

    # Pie chart for top categories
    plt.subplot(1, 2, 2)
    top_5 = df[col].value_counts().head(5)
    other_count = df[col].value_counts().iloc[5:].sum() if len(df[col].value_counts()) > 5 else 0
    if other_count > 0:
        top_5['Others'] = other_count
    top_5.plot(kind='pie', autopct='%1.1f%%')
    plt.title(f'Distribution of {{col}} (Top 5 + Others)')

    plt.tight_layout()
    plt.show()''')

        # Correlation heatmap
        if len(data_types.get("numeric", [])) > 1:
            code_parts.append('''
# Correlation analysis visualization
plt.figure(figsize=(12, 10))
correlation_matrix = df.select_dtypes(include=[np.number]).corr()

# Create mask for upper triangle
mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))

# Generate heatmap
sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='RdYlBu_r',
            center=0, square=True, linewidths=0.5)
plt.title('Correlation Matrix Heatmap')
plt.tight_layout()
plt.show()''')

        # Bivariate analysis
        if data_types.get("numeric") and data_types.get("categorical"):
            code_parts.append('''
# Bivariate analysis: Categorical vs Numerical
numerical_cols = df.select_dtypes(include=[np.number]).columns
categorical_cols = df.select_dtypes(include=['object']).columns

if len(numerical_cols) > 0 and len(categorical_cols) > 0:
    # Box plots
    fig, axes = plt.subplots(1, min(2, len(categorical_cols)), figsize=(15, 6))
    if len(categorical_cols) == 1:
        axes = [axes]

    for i, cat_col in enumerate(categorical_cols[:2]):
        if len(numerical_cols) > 0:
            ax = axes[i] if len(categorical_cols) > 1 else axes[0]
            df.boxplot(column=numerical_cols[0], by=cat_col, ax=ax)
            ax.set_title(f'{{numerical_cols[0]}} by {{cat_col}}')
            ax.set_xlabel(cat_col)

    plt.tight_layout()
    plt.show()''')

        # Interactive plots with Plotly
        code_parts.append('''
# Interactive visualizations with Plotly
print("Creating interactive visualizations...")

# Interactive scatter plot (if we have at least 2 numerical columns)
numerical_cols = df.select_dtypes(include=[np.number]).columns
if len(numerical_cols) >= 2:
    fig = px.scatter(df, x=numerical_cols[0], y=numerical_cols[1],
                     title=f'Interactive Scatter: {{numerical_cols[0]}} vs {{numerical_cols[1]}}')
    fig.show()

# Interactive histogram
if len(numerical_cols) > 0:
    fig = px.histogram(df, x=numerical_cols[0],
                       title=f'Interactive Histogram: {{numerical_cols[0]}}')
    fig.show()''')

        return "\n".join(code_parts)

    def _generate_ml_code(self, context: Dict[str, Any], user_request: Optional[str]) -> str:
        """Generate machine learning code"""

        data_types = context.get("data_types", {})
        potential_targets = context.get("potential_targets", [])

        code_parts = []

        # Data loading and preparation
        code_parts.append('''# Load and prepare data for machine learning
df = pd.read_csv('your_data.csv')
print("Dataset shape:", df.shape)

# Basic data exploration
print("\\nMissing values:")
print(df.isnull().sum())

# Handle missing values
df = df.dropna()  # Simple approach - adjust as needed
print("Shape after removing missing values:", df.shape)''')

        # Target variable selection
        if potential_targets:
            target_suggestion = potential_targets[0]
            code_parts.append(f'''
# Define target variable (suggested: {target_suggestion})
target_column = '{target_suggestion}'  # Adjust as needed
if target_column in df.columns:
    y = df[target_column]
    X = df.drop(columns=[target_column])
else:
    # If suggested target not available, please specify manually
    print("Available columns:", df.columns.tolist())
    target_column = input("Please enter target column name: ")
    y = df[target_column]
    X = df.drop(columns=[target_column])''')
        else:
            code_parts.append('''
# Define target variable (please specify)
print("Available columns:", df.columns.tolist())
target_column = 'your_target_column'  # CHANGE THIS
y = df[target_column]
X = df.drop(columns=[target_column])''')

        # Feature preprocessing
        code_parts.append('''
# Feature preprocessing
print("\\nFeature preprocessing...")

# Handle categorical variables
categorical_columns = X.select_dtypes(include=['object']).columns
numerical_columns = X.select_dtypes(include=[np.number]).columns

print(f"Categorical columns: {list(categorical_columns)}")
print(f"Numerical columns: {list(numerical_columns)}")

# Encode categorical variables
le_dict = {}
X_processed = X.copy()

for col in categorical_columns:
    le = LabelEncoder()
    X_processed[col] = le.fit_transform(X_processed[col].astype(str))
    le_dict[col] = le

# Scale numerical features
scaler = StandardScaler()
if len(numerical_columns) > 0:
    X_processed[numerical_columns] = scaler.fit_transform(X_processed[numerical_columns])

print("\\nProcessed feature shape:", X_processed.shape)''')

        # Train-test split and model training
        code_parts.append('''
# Split data into training and testing sets
X_train, X_test, y_train, y_test = train_test_split(
    X_processed, y, test_size=0.2, random_state=42
)

print(f"Training set size: {X_train.shape}")
print(f"Test set size: {X_test.shape}")

# Determine problem type and train models
unique_targets = y.nunique()
is_classification = unique_targets <= 10  # Heuristic for classification

print(f"\\nProblem type: {'Classification' if is_classification else 'Regression'}")
print(f"Unique target values: {unique_targets}")

models = {}
results = {}

if is_classification:
    # Classification models
    models['Random Forest'] = RandomForestClassifier(n_estimators=100, random_state=42)
    models['Logistic Regression'] = LogisticRegression(random_state=42, max_iter=1000)

    for name, model in models.items():
        print(f"\\nTraining {name}...")
        model.fit(X_train, y_train)

        # Predictions
        y_pred = model.predict(X_test)

        # Cross-validation score
        cv_scores = cross_val_score(model, X_train, y_train, cv=5)

        results[name] = {
            'model': model,
            'predictions': y_pred,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std()
        }

        print(f"{name} CV Score: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
        print("Classification Report:")
        print(classification_report(y_test, y_pred))

else:
    # Regression models
    models['Random Forest'] = RandomForestRegressor(n_estimators=100, random_state=42)
    models['Linear Regression'] = LinearRegression()

    for name, model in models.items():
        print(f"\\nTraining {name}...")
        model.fit(X_train, y_train)

        # Predictions
        y_pred = model.predict(X_test)

        # Metrics
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        results[name] = {
            'model': model,
            'predictions': y_pred,
            'mse': mse,
            'r2': r2
        }

        print(f"{name} - MSE: {mse:.4f}, R²: {r2:.4f}")

# Feature importance (for tree-based models)
best_model = results['Random Forest']['model']
feature_importance = pd.DataFrame({
    'feature': X.columns,
    'importance': best_model.feature_importances_
}).sort_values('importance', ascending=False)

print("\\nTop 10 Most Important Features:")
print(feature_importance.head(10))

# Plot feature importance
plt.figure(figsize=(10, 6))
feature_importance.head(10).plot(x='feature', y='importance', kind='bar')
plt.title('Top 10 Feature Importance')
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()''')

        return "\n".join(code_parts)

    def _generate_stats_code(self, context: Dict[str, Any], user_request: Optional[str]) -> str:
        """Generate statistical analysis code"""

        data_types = context.get("data_types", {})

        code_parts = []

        # Data loading
        code_parts.append('''# Load data for statistical analysis
df = pd.read_csv('your_data.csv')
print("Dataset shape:", df.shape)

# Basic data overview
print("\\nDataset Info:")
print(df.info())
print("\\nDescriptive Statistics:")
print(df.describe())''')

        # Normality tests
        if data_types.get("numeric"):
            code_parts.append(f'''
# Normality tests for numerical variables
numerical_cols = {data_types["numeric"]}
print("\\nNormality Tests (Shapiro-Wilk):")
print("-" * 40)

for col in numerical_cols:
    if col in df.columns and df[col].notna().sum() > 3:
        # Sample for large datasets
        sample_data = df[col].dropna().sample(min(5000, len(df[col].dropna())))
        stat, p_value = stats.shapiro(sample_data)
        print(f"{{col}}: Statistic={{stat:.4f}}, p-value={{p_value:.4f}}")
        if p_value > 0.05:
            print(f"  -> {{col}} appears to be normally distributed")
        else:
            print(f"  -> {{col}} does not appear to be normally distributed")''')

        # Correlation analysis with significance
        if len(data_types.get("numeric", [])) > 1:
            code_parts.append('''
# Correlation analysis with significance testing
numerical_data = df.select_dtypes(include=[np.number])
correlation_matrix = numerical_data.corr()

print("\\nCorrelation Matrix:")
print(correlation_matrix)

# Calculate p-values for correlations
from scipy.stats import pearsonr
n_vars = len(numerical_data.columns)
p_values = np.zeros((n_vars, n_vars))

for i, col1 in enumerate(numerical_data.columns):
    for j, col2 in enumerate(numerical_data.columns):
        if i != j:
            corr, p_val = pearsonr(numerical_data[col1].dropna(),
                                 numerical_data[col2].dropna())
            p_values[i, j] = p_val

# Create significance matrix
significance_matrix = pd.DataFrame(p_values,
                                 index=numerical_data.columns,
                                 columns=numerical_data.columns)

print("\\nCorrelation Significance (p-values):")
print(significance_matrix)

# Visualize correlation with significance
plt.figure(figsize=(12, 10))
mask = significance_matrix > 0.05  # Mask non-significant correlations
sns.heatmap(correlation_matrix, annot=True, cmap='RdYlBu_r', center=0,
            mask=mask, square=True, linewidths=0.5)
plt.title('Significant Correlations (p < 0.05)')
plt.tight_layout()
plt.show()''')

        # Group comparisons
        if data_types.get("categorical") and data_types.get("numeric"):
            code_parts.append('''
# Group comparisons (ANOVA and t-tests)
categorical_cols = df.select_dtypes(include=['object']).columns
numerical_cols = df.select_dtypes(include=[np.number]).columns

print("\\nGroup Comparison Analysis:")
print("-" * 40)

for cat_col in categorical_cols[:2]:  # Limit to first 2 categorical
    for num_col in numerical_cols[:2]:  # Limit to first 2 numerical
        groups = [group[num_col].dropna() for name, group in df.groupby(cat_col)]

        # Filter groups with sufficient data
        groups = [g for g in groups if len(g) >= 3]

        if len(groups) >= 2:
            print(f"\\nAnalyzing {num_col} across {cat_col} groups:")

            if len(groups) == 2:
                # T-test for 2 groups
                stat, p_value = ttest_ind(groups[0], groups[1])
                print(f"  T-test: statistic={stat:.4f}, p-value={p_value:.4f}")
            else:
                # ANOVA for multiple groups
                stat, p_value = f_oneway(*groups)
                print(f"  ANOVA: F-statistic={stat:.4f}, p-value={p_value:.4f}")

            if p_value < 0.05:
                print(f"  -> Significant difference found (p < 0.05)")
            else:
                print(f"  -> No significant difference (p >= 0.05)")''')

        # Chi-square tests for categorical associations
        if len(data_types.get("categorical", [])) >= 2:
            code_parts.append('''
# Chi-square tests for categorical associations
categorical_cols = df.select_dtypes(include=['object']).columns

if len(categorical_cols) >= 2:
    print("\\nCategorical Association Tests (Chi-square):")
    print("-" * 50)

    for i, col1 in enumerate(categorical_cols[:3]):
        for col2 in categorical_cols[i+1:4]:
            # Create contingency table
            contingency_table = pd.crosstab(df[col1], df[col2])

            # Chi-square test
            chi2, p_value, dof, expected = chi2_contingency(contingency_table)

            print(f"\\n{col1} vs {col2}:")
            print(f"  Chi-square: {chi2:.4f}")
            print(f"  p-value: {p_value:.4f}")
            print(f"  Degrees of freedom: {dof}")

            if p_value < 0.05:
                print(f"  -> Significant association (p < 0.05)")
            else:
                print(f"  -> No significant association (p >= 0.05)")''')

        # Summary statistics and confidence intervals
        code_parts.append('''
# Summary statistics with confidence intervals
print("\\nSummary Statistics with 95% Confidence Intervals:")
print("-" * 55)

for col in df.select_dtypes(include=[np.number]).columns:
    data = df[col].dropna()
    if len(data) > 1:
        mean = data.mean()
        std_err = stats.sem(data)
        ci = stats.t.interval(0.95, len(data)-1, loc=mean, scale=std_err)

        print(f"\\n{col}:")
        print(f"  Mean: {mean:.4f}")
        print(f"  95% CI: [{ci[0]:.4f}, {ci[1]:.4f}]")
        print(f"  Median: {data.median():.4f}")
        print(f"  Std Dev: {data.std():.4f}")''')

        return "\n".join(code_parts)
