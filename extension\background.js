/**
 * Background script for Kaggle Data Analysis Assistant
 * Handles API communication and extension lifecycle
 */

// Configuration
const CONFIG = {
  API_BASE_URL: 'http://localhost:8000',  // Analysis engine URL
  N8N_WEBHOOK_URL: 'https://sabryroby.app.n8n.cloud/webhook-test/kaggle-analysis',  // n8n webhook
  TIMEOUT: 30000  // 30 seconds timeout
};

// Extension installation/update handler
chrome.runtime.onInstalled.addListener((details) => {
  console.log('Kaggle Data Analysis Assistant installed/updated');
  
  // Set default configuration
  chrome.storage.sync.set({
    apiUrl: CONFIG.API_BASE_URL,
    n8nUrl: CONFIG.N8N_WEBHOOK_URL,
    useN8n: true,  // Enable n8n by default
    autoDetect: true,
    defaultAnalysisType: 'eda'
  });
});

// Message handler for communication with content script and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request);
  
  switch (request.action) {
    case 'analyzeData':
      handleAnalysisRequest(request.data, sendResponse);
      return true; // Keep message channel open for async response
      
    case 'getConfig':
      getConfiguration(sendResponse);
      return true;
      
    case 'updateConfig':
      updateConfiguration(request.config, sendResponse);
      return true;
      
    case 'detectKaggleData':
      detectKaggleData(sender.tab.id, sendResponse);
      return true;
      
    default:
      sendResponse({ error: 'Unknown action' });
  }
});

/**
 * Handle data analysis request
 */
async function handleAnalysisRequest(analysisData, sendResponse) {
  try {
    console.log('Processing analysis request:', analysisData);
    
    // Get current configuration
    const config = await getStoredConfig();
    
    // Prepare request payload
    const requestPayload = {
      data_info: analysisData.dataInfo,
      analysis_type: analysisData.analysisType || 'eda',
      user_request: analysisData.userRequest,
      data_source: analysisData.dataSource || 'metadata_only',
      file_type: analysisData.fileType,
      constraints: analysisData.constraints
    };
    
    // Send request to analysis engine (direct or via n8n)
    let response;
    if (config.useN8n) {
      response = await sendToN8n(requestPayload, config.n8nUrl);
    } else {
      response = await sendToAnalysisEngine(requestPayload, config.apiUrl);
    }
    
    // Log successful analysis
    logAnalysisEvent('success', analysisData.analysisType);
    
    sendResponse({
      success: true,
      data: response
    });
    
  } catch (error) {
    console.error('Analysis request failed:', error);
    
    // Log failed analysis
    logAnalysisEvent('error', analysisData.analysisType, error.message);
    
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Send request to analysis engine directly
 */
async function sendToAnalysisEngine(payload, apiUrl) {
  const response = await fetch(`${apiUrl}/analyze`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
    signal: AbortSignal.timeout(CONFIG.TIMEOUT)
  });
  
  if (!response.ok) {
    throw new Error(`Analysis engine error: ${response.status} ${response.statusText}`);
  }
  
  return await response.json();
}

/**
 * Send request via n8n webhook
 */
async function sendToN8n(payload, webhookUrl) {
  const response = await fetch(webhookUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      source: 'kaggle-extension',
      timestamp: new Date().toISOString(),
      payload: payload
    }),
    signal: AbortSignal.timeout(CONFIG.TIMEOUT)
  });
  
  if (!response.ok) {
    throw new Error(`n8n webhook error: ${response.status} ${response.statusText}`);
  }
  
  return await response.json();
}

/**
 * Get stored configuration
 */
async function getStoredConfig() {
  return new Promise((resolve) => {
    chrome.storage.sync.get([
      'apiUrl', 'n8nUrl', 'useN8n', 'autoDetect', 'defaultAnalysisType'
    ], (result) => {
      resolve({
        apiUrl: result.apiUrl || CONFIG.API_BASE_URL,
        n8nUrl: result.n8nUrl || CONFIG.N8N_WEBHOOK_URL,
        useN8n: result.useN8n || false,
        autoDetect: result.autoDetect || true,
        defaultAnalysisType: result.defaultAnalysisType || 'eda'
      });
    });
  });
}

/**
 * Get configuration for popup/content script
 */
async function getConfiguration(sendResponse) {
  try {
    const config = await getStoredConfig();
    sendResponse({ success: true, config });
  } catch (error) {
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * Update configuration
 */
function updateConfiguration(newConfig, sendResponse) {
  chrome.storage.sync.set(newConfig, () => {
    if (chrome.runtime.lastError) {
      sendResponse({ success: false, error: chrome.runtime.lastError.message });
    } else {
      sendResponse({ success: true });
    }
  });
}

/**
 * Detect Kaggle data in current tab
 */
async function detectKaggleData(tabId, sendResponse) {
  try {
    // Inject detection script into Kaggle page
    const results = await chrome.scripting.executeScript({
      target: { tabId: tabId },
      files: ['utils/kaggle-detector.js']
    });
    
    sendResponse({
      success: true,
      data: results[0].result
    });
    
  } catch (error) {
    console.error('Data detection failed:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Log analysis events for analytics
 */
function logAnalysisEvent(type, analysisType, errorMessage = null) {
  const event = {
    timestamp: new Date().toISOString(),
    type: type,
    analysisType: analysisType,
    error: errorMessage
  };
  
  // Store in local storage for analytics
  chrome.storage.local.get(['analyticsEvents'], (result) => {
    const events = result.analyticsEvents || [];
    events.push(event);
    
    // Keep only last 100 events
    if (events.length > 100) {
      events.splice(0, events.length - 100);
    }
    
    chrome.storage.local.set({ analyticsEvents: events });
  });
}
