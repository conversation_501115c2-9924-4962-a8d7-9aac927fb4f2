"""
Analysis plan generator - creates step-by-step analysis plans
"""

from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)


class PlanGenerator:
    """Generates analysis plans based on data characteristics and user requirements"""
    
    def __init__(self):
        self.plan_templates = {
            "eda": self._generate_eda_plan,
            "visualization": self._generate_visualization_plan,
            "ml": self._generate_ml_plan,
            "stats": self._generate_stats_plan
        }
    
    def generate_plan(
        self, 
        analysis_type: str, 
        context: Dict[str, Any], 
        user_request: Optional[str] = None
    ) -> str:
        """
        Generate analysis plan based on type and context
        
        Args:
            analysis_type: Type of analysis (eda, visualization, ml, stats)
            context: Analysis context from DataAnalyzer
            user_request: Specific user request or question
            
        Returns:
            Formatted analysis plan as string
        """
        try:
            if analysis_type not in self.plan_templates:
                raise ValueError(f"Unsupported analysis type: {analysis_type}")
            
            # Generate base plan
            plan_generator = self.plan_templates[analysis_type]
            plan_steps = plan_generator(context, user_request)
            
            # Format plan
            formatted_plan = self._format_plan(analysis_type, plan_steps, context)
            
            return formatted_plan
            
        except Exception as e:
            logger.error(f"Error generating plan: {str(e)}")
            raise
    
    def _generate_eda_plan(self, context: Dict[str, Any], user_request: Optional[str]) -> List[str]:
        """Generate Exploratory Data Analysis plan"""
        steps = []
        
        # Data loading and basic info
        if context.get("suggested_sampling", {}).get("needed"):
            steps.append("Load and sample the dataset for efficient analysis")
        else:
            steps.append("Load the complete dataset")
        
        steps.extend([
            "Display basic dataset information (shape, columns, data types)",
            "Check for missing values and data quality issues",
            "Generate descriptive statistics for numerical columns"
        ])
        
        # Data type specific analysis
        data_types = context.get("data_types", {})
        
        if data_types.get("categorical"):
            steps.append("Analyze categorical variables (value counts, unique values)")
        
        if data_types.get("numeric"):
            steps.append("Analyze numerical variables (distributions, outliers)")
        
        if data_types.get("datetime"):
            steps.append("Analyze temporal patterns in datetime columns")
        
        # Correlation and relationships
        if len(data_types.get("numeric", [])) > 1:
            steps.append("Calculate correlation matrix for numerical variables")
        
        # Data visualization
        steps.extend([
            "Create distribution plots for key variables",
            "Generate correlation heatmap if applicable",
            "Create summary visualizations for categorical variables"
        ])
        
        # User-specific requests
        if user_request:
            steps.append(f"Address specific user question: {user_request}")
        
        steps.append("Provide insights and recommendations for further analysis")
        
        return steps
    
    def _generate_visualization_plan(self, context: Dict[str, Any], user_request: Optional[str]) -> List[str]:
        """Generate Data Visualization plan"""
        steps = []
        
        # Setup
        steps.extend([
            "Load and prepare the dataset for visualization",
            "Set up plotting libraries and style configurations"
        ])
        
        data_types = context.get("data_types", {})
        
        # Univariate visualizations
        if data_types.get("numeric"):
            steps.extend([
                "Create histograms and box plots for numerical variables",
                "Generate distribution plots with statistical overlays"
            ])
        
        if data_types.get("categorical"):
            steps.extend([
                "Create bar charts and pie charts for categorical variables",
                "Generate count plots for categorical distributions"
            ])
        
        # Bivariate visualizations
        if len(data_types.get("numeric", [])) > 1:
            steps.extend([
                "Create scatter plots for numerical variable relationships",
                "Generate correlation heatmap with annotations"
            ])
        
        if data_types.get("categorical") and data_types.get("numeric"):
            steps.append("Create box plots and violin plots for categorical vs numerical analysis")
        
        # Advanced visualizations
        if context.get("row_count", 0) > 1000:
            steps.append("Create density plots and 2D histograms for large datasets")
        
        if data_types.get("datetime"):
            steps.append("Generate time series plots and temporal trend analysis")
        
        # Interactive elements
        steps.extend([
            "Add interactive elements using Plotly where appropriate",
            "Create dashboard-style multi-panel visualizations"
        ])
        
        if user_request:
            steps.append(f"Create custom visualizations based on user request: {user_request}")
        
        steps.append("Provide interpretation and insights from visualizations")
        
        return steps
    
    def _generate_ml_plan(self, context: Dict[str, Any], user_request: Optional[str]) -> List[str]:
        """Generate Machine Learning plan"""
        steps = []
        
        # Data preparation
        steps.extend([
            "Load and explore the dataset structure",
            "Handle missing values and data quality issues",
            "Perform feature engineering and data preprocessing"
        ])
        
        # Target variable identification
        potential_targets = context.get("potential_targets", [])
        if potential_targets:
            steps.append(f"Identify target variable (suggested: {', '.join(potential_targets[:3])})")
        else:
            steps.append("Define the prediction target based on business requirements")
        
        # Feature analysis
        steps.extend([
            "Analyze feature distributions and relationships",
            "Handle categorical variables (encoding, scaling)",
            "Feature selection and dimensionality reduction if needed"
        ])
        
        # Model development
        data_types = context.get("data_types", {})
        if len(data_types.get("categorical", [])) > 0:
            steps.append("Implement classification models (Random Forest, Logistic Regression, XGBoost)")
        
        if len(data_types.get("numeric", [])) > 2:
            steps.append("Implement regression models if target is continuous")
        
        # Model evaluation
        steps.extend([
            "Split data into training and testing sets",
            "Train multiple models and compare performance",
            "Perform cross-validation and hyperparameter tuning",
            "Evaluate models using appropriate metrics",
            "Generate feature importance analysis"
        ])
        
        # Results and interpretation
        steps.extend([
            "Create model performance visualizations",
            "Interpret model results and feature contributions",
            "Provide recommendations for model deployment"
        ])
        
        if user_request:
            steps.append(f"Address specific ML requirements: {user_request}")
        
        return steps
    
    def _generate_stats_plan(self, context: Dict[str, Any], user_request: Optional[str]) -> List[str]:
        """Generate Statistical Analysis plan"""
        steps = []
        
        # Data preparation
        steps.extend([
            "Load dataset and check statistical assumptions",
            "Handle missing values and outliers appropriately",
            "Verify data distributions and normality"
        ])
        
        data_types = context.get("data_types", {})
        
        # Descriptive statistics
        steps.extend([
            "Calculate comprehensive descriptive statistics",
            "Generate confidence intervals for key metrics"
        ])
        
        # Statistical tests
        if len(data_types.get("numeric", [])) >= 2:
            steps.extend([
                "Perform correlation analysis with significance testing",
                "Conduct t-tests for group comparisons if applicable"
            ])
        
        if data_types.get("categorical"):
            steps.append("Perform chi-square tests for categorical associations")
        
        # Advanced statistical analysis
        if context.get("row_count", 0) > 100:
            steps.extend([
                "Conduct ANOVA for multiple group comparisons",
                "Perform regression analysis for relationship modeling"
            ])
        
        # Hypothesis testing
        steps.extend([
            "Define and test relevant statistical hypotheses",
            "Calculate effect sizes and practical significance",
            "Perform multiple comparison corrections if needed"
        ])
        
        # Results interpretation
        steps.extend([
            "Interpret statistical results in practical context",
            "Create statistical summary tables and plots",
            "Provide recommendations based on statistical findings"
        ])
        
        if user_request:
            steps.append(f"Address specific statistical questions: {user_request}")
        
        return steps
    
    def _format_plan(self, analysis_type: str, steps: List[str], context: Dict[str, Any]) -> str:
        """Format the analysis plan into a readable string"""
        
        # Header
        plan_title = {
            "eda": "Exploratory Data Analysis Plan",
            "visualization": "Data Visualization Plan", 
            "ml": "Machine Learning Analysis Plan",
            "stats": "Statistical Analysis Plan"
        }.get(analysis_type, "Analysis Plan")
        
        # Context summary
        data_shape = context.get("data_shape", "Unknown")
        column_count = context.get("column_count", "Unknown")
        
        header = f"""
{plan_title}
{'=' * len(plan_title)}

Dataset Overview:
- Shape: {data_shape}
- Columns: {column_count}
- File Type: {context.get('file_type', 'Unknown')}
"""
        
        # Sampling note
        if context.get("suggested_sampling", {}).get("needed"):
            sampling_info = context["suggested_sampling"]
            header += f"- Sampling: {sampling_info['sample_size']} rows (due to large dataset)\n"
        
        # Steps
        steps_text = "\nAnalysis Steps:\n"
        for i, step in enumerate(steps, 1):
            steps_text += f"{i}. {step}\n"
        
        # Footer
        footer = f"\nEstimated Time: {len(steps) * 2}-{len(steps) * 3} minutes"
        footer += f"\nLibraries Used: pandas, matplotlib, seaborn"

        if analysis_type == "ml":
            footer += ", scikit-learn"
        elif analysis_type == "stats":
            footer += ", scipy"

        return header + steps_text + footer
