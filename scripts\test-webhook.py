#!/usr/bin/env python3
"""
Simple test script to verify n8n webhook connectivity
"""

import requests
import json
import time

# Your n8n webhook URL
WEBHOOK_URL = "https://sabryroby.app.n8n.cloud/webhook-test/kaggle-analysis"

def test_webhook_basic():
    """Test basic webhook connectivity"""
    print("🔗 Testing n8n webhook connectivity...")
    print(f"URL: {WEBHOOK_URL}")
    
    # Simple test payload
    test_payload = {
        "test": True,
        "timestamp": time.time(),
        "message": "Hello from Kaggle Analysis Assistant"
    }
    
    try:
        print("Sending test request...")
        response = requests.post(WEBHOOK_URL, json=test_payload, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Webhook is accessible and responding")
            try:
                result = response.json()
                print(f"Response JSON: {json.dumps(result, indent=2)}")
            except:
                print(f"Response Text: {response.text}")
            return True
            
        elif response.status_code == 404:
            print("❌ Webhook not found (404)")
            try:
                error_info = response.json()
                print(f"Error details: {json.dumps(error_info, indent=2)}")
                
                if "not registered" in error_info.get("message", ""):
                    print("\n💡 This means you need to:")
                    print("1. Import the workflow into n8n")
                    print("2. Activate the workflow")
                    print("3. Make sure the webhook path is 'kaggle-analysis'")
                    
            except:
                print(f"Error response: {response.text}")
            return False
            
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {e}")
        return False

def test_webhook_with_analysis_payload():
    """Test webhook with a realistic analysis payload"""
    print("\n🧪 Testing webhook with analysis payload...")
    
    # Realistic analysis payload
    analysis_payload = {
        "source": "test_script",
        "timestamp": time.time(),
        "payload": {
            "data_info": {
                "columns": ["id", "name", "age", "salary"],
                "dtypes": {
                    "id": "int64",
                    "name": "object", 
                    "age": "int64",
                    "salary": "float64"
                },
                "shape": [1000, 4],
                "sample_data": [
                    {"id": 1, "name": "John", "age": 25, "salary": 50000},
                    {"id": 2, "name": "Jane", "age": 30, "salary": 60000}
                ]
            },
            "analysis_type": "eda",
            "user_request": "Analyze employee data",
            "data_source": "test",
            "file_type": "csv"
        }
    }
    
    try:
        print("Sending analysis payload...")
        response = requests.post(WEBHOOK_URL, json=analysis_payload, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Webhook accepted analysis payload")
            try:
                result = response.json()
                if "plan" in result and "code" in result:
                    print("✅ Received proper analysis response!")
                    print(f"Plan length: {len(result['plan'])} characters")
                    print(f"Code length: {len(result['code'])} characters")
                else:
                    print("⚠️  Response format may be incorrect")
                    print(f"Response keys: {list(result.keys())}")
            except:
                print(f"Response: {response.text[:200]}...")
            return True
        else:
            print(f"❌ Failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

def main():
    """Run webhook tests"""
    print("🚀 n8n Webhook Test")
    print("=" * 40)
    
    # Run tests
    tests = [
        ("Basic Connectivity", test_webhook_basic),
        ("Analysis Payload", test_webhook_with_analysis_payload),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 TEST RESULTS")
    print("=" * 40)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<20} {status}")
    
    if all(results.values()):
        print("\n🎉 All webhook tests passed!")
        print("Your n8n webhook is working correctly.")
    else:
        print("\n⚠️  Some tests failed.")
        if not results.get("Basic Connectivity"):
            print("\n💡 Next steps:")
            print("1. Go to https://sabryroby.app.n8n.cloud/")
            print("2. Import n8n-workflows/analysis-workflow.json")
            print("3. Activate the workflow")
            print("4. Make sure webhook path is 'kaggle-analysis'")
    
    return 0 if all(results.values()) else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
