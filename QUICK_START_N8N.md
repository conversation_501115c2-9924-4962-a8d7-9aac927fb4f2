# Quick Start with n8n Cloud

This guide will get you up and running with the Kaggle Data Analysis Assistant using your n8n cloud instance.

## 🚀 Prerequisites

- Your n8n cloud instance: `https://sabryroby.app.n8n.cloud/`
- Chrome browser
- Python 3.8+ (for the analysis engine)

## ⚡ Quick Setup (5 minutes)

### 1. Start the Analysis Engine

```bash
cd analysis-engine
pip install -r requirements.txt
python main.py
```

Keep this running in a terminal. The engine will be available at `http://localhost:8000`.

### 2. Test n8n Connectivity

```bash
python scripts/setup-n8n.py
```

This script will:
- Test your n8n webhook connectivity
- Verify the analysis engine is running
- Generate setup instructions

### 3. Import Workflow to n8n

1. **Go to your n8n instance**: https://sabryroby.app.n8n.cloud/
2. **Login** to your account
3. **Navigate to Workflows** (sidebar)
4. **Click "Import from file"**
5. **Upload**: `n8n-workflows/analysis-workflow.json`
6. **Open the imported workflow**
7. **Set it to "Active"** (toggle in top-right)

### 4. Configure Environment Variables

In your n8n cloud instance:

1. **Go to Settings** → **Environment Variables**
2. **Add variable**:
   - Name: `ANALYSIS_ENGINE_URL`
   - Value: `http://localhost:8000`
   
   > **Note**: If your analysis engine is deployed elsewhere, use that URL instead.

### 5. Install Chrome Extension

1. **Open Chrome** → **Extensions** (`chrome://extensions/`)
2. **Enable "Developer mode"** (top-right toggle)
3. **Click "Load unpacked"**
4. **Select the `extension` folder** from this project
5. **Pin the extension** to your toolbar

### 6. Verify Configuration

The extension is pre-configured with:
- ✅ **n8n Webhook**: `https://sabryroby.app.n8n.cloud/webhook-test/kaggle-analysis`
- ✅ **Use n8n Backend**: Enabled by default
- ✅ **Auto-detect Data**: Enabled

## 🧪 Test the System

### Option 1: Run Test Script
```bash
python scripts/test-system.py
```

### Option 2: Manual Test
1. **Go to any Kaggle notebook**
2. **Click the extension icon** (or the floating "Analyze Data" button)
3. **Click "Detect Data"** or **"Quick EDA"**
4. **Check the results**

### Option 3: Run Demo
```bash
python scripts/demo.py
```

## 🔧 Workflow Details

Your n8n workflow will:

1. **Receive requests** from the Chrome extension
2. **Validate** the request data
3. **Call your analysis engine** at `http://localhost:8000`
4. **Process the response** and format it
5. **Return results** to the extension

## 📊 Expected Workflow

```
Chrome Extension → n8n Cloud → Local Analysis Engine → n8n Cloud → Chrome Extension
```

## ⚠️ Troubleshooting

### Extension shows "Disconnected"
- **Check**: Is the analysis engine running? (`python main.py`)
- **Check**: Is the n8n workflow active?
- **Test**: Run `python scripts/setup-n8n.py`

### n8n workflow fails
- **Check**: Environment variable `ANALYSIS_ENGINE_URL` is set correctly
- **Check**: Your analysis engine is accessible from the internet (if needed)
- **Check**: Workflow is imported and active

### Analysis engine not accessible from n8n
If your analysis engine is running locally (`localhost:8000`), n8n cloud cannot reach it directly. Options:

1. **Use ngrok** to expose your local server:
   ```bash
   ngrok http 8000
   ```
   Then update the environment variable to the ngrok URL.

2. **Deploy analysis engine** to a cloud service (Heroku, Railway, etc.)

3. **Use direct mode**: Disable n8n in extension settings and connect directly to your local analysis engine.

## 🎯 Next Steps

Once everything is working:

1. **Visit a Kaggle notebook** with data
2. **Click the "Analyze Data" button**
3. **Select your data source**
4. **Choose analysis type** (EDA, ML, Visualization, Stats)
5. **Get instant analysis plans and code!**

## 📞 Support

If you encounter issues:

1. **Check the troubleshooting section** above
2. **Run the test scripts** to identify the problem
3. **Check n8n execution logs** in your cloud instance
4. **Verify all URLs and configurations** are correct

## 🔗 Important URLs

- **n8n Cloud Instance**: https://sabryroby.app.n8n.cloud/
- **Webhook URL**: https://sabryroby.app.n8n.cloud/webhook-test/kaggle-analysis
- **Local Analysis Engine**: http://localhost:8000
- **Analysis Engine Health**: http://localhost:8000/health

---

**You're all set! 🎉 The Kaggle Data Analysis Assistant is ready to accelerate your data science workflow.**
