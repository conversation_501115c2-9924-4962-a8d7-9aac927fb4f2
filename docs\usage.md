# Usage Guide

Learn how to use the Kaggle Data Analysis Assistant to accelerate your data science workflow.

## Getting Started

### 1. Open a Kaggle Notebook

Navigate to any Kaggle notebook that contains data files or datasets.

### 2. Activate the Assistant

Click the "Analyze Data" button that appears in the top-right corner of the page, or click the extension icon in your Chrome toolbar.

## Main Features

### Data Detection

The assistant automatically detects:

- **Uploaded Files**: CSV, Excel, JSON files you've uploaded
- **Dataset Files**: Files from Kaggle datasets
- **DataFrames**: Variables created with `pd.read_csv()`, `pd.read_excel()`, etc.
- **Data Variables**: Arrays, lists, and other data structures

#### Manual Data Detection

If automatic detection misses something:

1. Click "Refresh Detection" in the assistant panel
2. Or use the "Detect Data" button in the popup

### Analysis Types

Choose from four analysis types:

#### 1. Exploratory Data Analysis (EDA)
- **Purpose**: Comprehensive data exploration
- **Includes**: 
  - Dataset overview and basic statistics
  - Missing value analysis
  - Data type analysis
  - Correlation analysis
  - Basic visualizations

**Example Output**:
```python
# Load the complete dataset
df = pd.read_csv('your_data.csv')

# Basic dataset information
print("Dataset Shape:", df.shape)
print("\nColumn Names:")
print(df.columns.tolist())

# Missing values analysis
print("\nMissing Values:")
missing_data = df.isnull().sum()
# ... more code
```

#### 2. Data Visualization
- **Purpose**: Create insightful charts and plots
- **Includes**:
  - Distribution plots
  - Correlation heatmaps
  - Categorical analysis
  - Interactive visualizations with Plotly

**Example Output**:
```python
# Interactive visualizations with Plotly
import plotly.express as px

# Interactive scatter plot
fig = px.scatter(df, x='column1', y='column2', 
                 title='Interactive Scatter Plot')
fig.show()
# ... more code
```

#### 3. Machine Learning
- **Purpose**: Build predictive models
- **Includes**:
  - Data preprocessing
  - Feature engineering
  - Model training and evaluation
  - Feature importance analysis

**Example Output**:
```python
# Feature preprocessing
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier

# Define target variable
target_column = 'target'
y = df[target_column]
X = df.drop(columns=[target_column])
# ... more code
```

#### 4. Statistical Analysis
- **Purpose**: Hypothesis testing and statistical inference
- **Includes**:
  - Normality tests
  - Correlation significance testing
  - Group comparisons (t-tests, ANOVA)
  - Confidence intervals

**Example Output**:
```python
# Normality tests
from scipy import stats

for col in numerical_cols:
    stat, p_value = stats.shapiro(df[col].dropna())
    print(f"{col}: p-value={p_value:.4f}")
# ... more code
```

### Custom Requests

Add specific questions or requirements in the "Specific Request" field:

**Examples**:
- "Focus on predicting customer churn"
- "Compare sales performance across regions"
- "Analyze seasonal trends in the data"
- "Find the most important features for price prediction"

## Step-by-Step Workflow

### 1. Data Selection

1. Open the assistant panel
2. Review detected data sources
3. Click on a data source to select it
4. Selected items will be highlighted in blue

### 2. Analysis Configuration

1. Choose analysis type from dropdown
2. Add any specific requests (optional)
3. Click "Generate Analysis"

### 3. Review Results

The assistant provides:

- **Analysis Plan**: Step-by-step breakdown of what will be analyzed
- **Python Code**: Ready-to-run code for your notebook

### 4. Use the Code

Two options for using the generated code:

#### Option A: Copy and Paste
1. Click "Copy Code"
2. Paste into a new cell in your Kaggle notebook
3. Run the cell

#### Option B: Direct Insertion (Coming Soon)
1. Click "Insert to Notebook"
2. Code will be automatically added to your notebook

## Advanced Features

### Large Dataset Handling

For datasets with >100,000 rows:

- **Automatic Sampling**: The system automatically samples data for faster analysis
- **Sample Size**: Typically 50,000 rows or 50% of the dataset
- **Notification**: You'll be informed when sampling is applied

**Example**:
```python
# Load data with sampling (large dataset detected)
df = pd.read_csv('your_data.csv', nrows=50000)
print(f"Loaded sample of {len(df)} rows for analysis")
```

### Multiple File Analysis

When multiple data files are detected:

1. Select the primary file for analysis
2. The assistant will focus on that file
3. You can run separate analyses for other files

### Iterative Analysis

Build on previous analyses:

1. Run initial EDA
2. Based on insights, run focused visualization
3. Proceed to machine learning if patterns are found
4. Validate with statistical analysis

## Best Practices

### 1. Start with EDA

Always begin with Exploratory Data Analysis to understand your data structure and quality.

### 2. Use Specific Requests

The more specific your request, the better the generated code:

❌ **Vague**: "Analyze the data"
✅ **Specific**: "Predict house prices using square footage, location, and number of bedrooms"

### 3. Review Generated Code

Always review the generated code before running:

- Check variable names match your data
- Verify the target variable is correct
- Adjust parameters as needed

### 4. Customize as Needed

The generated code is a starting point. Feel free to:

- Modify visualization styles
- Adjust model parameters
- Add additional analysis steps

## Troubleshooting

### Data Not Detected

**Issue**: Assistant doesn't see your data files

**Solutions**:
1. Ensure files are properly uploaded to Kaggle
2. Check that file extensions are supported (.csv, .xlsx, .json)
3. Try the "Refresh Detection" button
4. Manually reference files in code cells first

### Code Doesn't Run

**Issue**: Generated code produces errors

**Common Fixes**:
1. **File Path**: Update file paths to match your actual files
2. **Column Names**: Verify column names match your data
3. **Dependencies**: Install missing libraries if needed
4. **Data Types**: Check for data type mismatches

### Performance Issues

**Issue**: Analysis takes too long

**Solutions**:
1. Use data sampling for large datasets
2. Focus on specific columns of interest
3. Use simpler analysis types first

## Tips for Better Results

### 1. Prepare Your Data

- Upload clean, well-formatted data files
- Use descriptive column names
- Include data dictionaries when possible

### 2. Be Specific

- Clearly state your analysis goals
- Mention the business context
- Specify what you want to predict or understand

### 3. Iterate

- Start broad with EDA
- Narrow down to specific questions
- Build complexity gradually

### 4. Validate Results

- Always verify the generated insights
- Cross-check with domain knowledge
- Test assumptions with additional analysis

## Next Steps

- Explore the [API Reference](api-reference.md) for advanced usage
- Check out [Development Guide](development.md) to customize the assistant
- See example notebooks in the `examples/` directory
