# Project Structure

```
kaggle-analysis-assistant/
├── README.md
├── project-structure.md
├── extension/                          # Chrome Extension
│   ├── manifest.json                   # Extension manifest
│   ├── background.js                   # Background script
│   ├── content.js                      # Content script for Kaggle integration
│   ├── popup/                          # Extension popup UI
│   │   ├── popup.html
│   │   ├── popup.css
│   │   └── popup.js
│   ├── icons/                          # Extension icons
│   │   ├── icon16.png
│   │   ├── icon48.png
│   │   └── icon128.png
│   └── utils/                          # Utility functions
│       ├── api.js                      # API communication
│       └── kaggle-detector.js          # Kaggle notebook detection
├── analysis-engine/                    # Python Analysis Engine
│   ├── main.py                         # Main analysis service
│   ├── requirements.txt                # Python dependencies
│   ├── core/                           # Core analysis modules
│   │   ├── __init__.py
│   │   ├── analyzer.py                 # Main analyzer class
│   │   ├── plan_generator.py           # Analysis plan generation
│   │   └── code_generator.py           # Python code generation
│   ├── processors/                     # Data processors
│   │   ├── __init__.py
│   │   ├── csv_processor.py            # CSV data handling
│   │   ├── excel_processor.py          # Excel data handling
│   │   └── json_processor.py           # JSON data handling
│   ├── templates/                      # Analysis templates
│   │   ├── eda_template.py             # Exploratory Data Analysis
│   │   ├── visualization_template.py   # Data visualization
│   │   ├── ml_template.py              # Machine learning
│   │   └── stats_template.py           # Statistical analysis
│   ├── utils/                          # Utility functions
│   │   ├── __init__.py
│   │   ├── data_utils.py               # Data manipulation utilities
│   │   └── sampling.py                 # Data sampling strategies
│   └── tests/                          # Unit tests
│       ├── __init__.py
│       ├── test_analyzer.py
│       ├── test_processors.py
│       └── test_templates.py
├── n8n-workflows/                      # n8n Backend Workflows
│   ├── analysis-workflow.json          # Main analysis workflow
│   ├── data-processing-workflow.json   # Data processing workflow
│   └── response-formatting-workflow.json # Response formatting
├── api/                                # API Documentation & Schemas
│   ├── openapi.yaml                    # API specification
│   ├── schemas/                        # JSON schemas
│   │   ├── request-schema.json
│   │   └── response-schema.json
│   └── examples/                       # API examples
│       ├── sample-requests.json
│       └── sample-responses.json
├── docs/                               # Documentation
│   ├── installation.md                 # Installation guide
│   ├── usage.md                        # Usage instructions
│   ├── api-reference.md                # API reference
│   └── development.md                  # Development guide
├── examples/                           # Example data and notebooks
│   ├── sample-data/                    # Sample datasets
│   │   ├── titanic.csv
│   │   ├── sales-data.xlsx
│   │   └── user-behavior.json
│   └── sample-notebooks/               # Example Kaggle notebooks
│       ├── eda-example.ipynb
│       └── ml-example.ipynb
└── scripts/                            # Build and deployment scripts
    ├── build-extension.sh              # Build Chrome extension
    ├── deploy-analysis-engine.sh       # Deploy Python service
    └── setup-n8n.sh                    # Setup n8n workflows
```

## Component Responsibilities

### Chrome Extension
- **Content Script**: Detects Kaggle notebook environment, extracts data information
- **Background Script**: Manages API calls to n8n backend
- **Popup UI**: Provides user interface for analysis requests and configuration

### Analysis Engine
- **Core Analyzer**: Main logic for analyzing data and generating plans
- **Processors**: Handle different data formats and extract metadata
- **Templates**: Provide reusable analysis patterns for different use cases
- **Utils**: Common utilities for data manipulation and sampling

### n8n Backend
- **Workflows**: Orchestrate the analysis process and handle communication
- **Data Processing**: Validate and preprocess incoming requests
- **Response Formatting**: Format analysis results for the Chrome extension

## Data Flow

1. **User Interaction**: User interacts with Chrome extension in Kaggle notebook
2. **Data Detection**: Extension detects available data files and structure
3. **Request Formation**: Extension creates analysis request with data metadata
4. **n8n Processing**: n8n workflow receives and validates request
5. **Analysis Engine**: Python service generates analysis plan and code
6. **Response Delivery**: Results are formatted and sent back to extension
7. **Code Injection**: Extension displays results and optionally injects code into notebook
